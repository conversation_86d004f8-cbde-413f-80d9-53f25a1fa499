package com.scube.payment.features.payment.processing.dto;

import com.scube.payment.features.payment.storage.model.Payee;
import com.scube.payment.features.payment.storage.model.Payment;
import lombok.Data;
import lombok.NoArgsConstructor;
import org.apache.commons.lang3.ObjectUtils;
import org.springframework.beans.BeanUtils;

import java.math.BigDecimal;
import java.time.Instant;
import java.util.UUID;

@Data
@NoArgsConstructor
public class GetPaymentResponseDto {
    private UUID paymentId;
    private UUID orderId;
    private String orderNumber;
    private String transactionId;
    private Instant transactionDate;
    private String status;
    private String paymentType;
    private BigDecimal amount;
    private String paymentProvider;
    private UUID receiptId;
    private PayeeDto payee;
    private String paymentNumber;
    private String createdBy;
    private String paymentReference;
    private Boolean isOnlineTransaction;

    public GetPaymentResponseDto(Payment payment) {
        if (!ObjectUtils.isEmpty(payment.getStatus())) {
            this.status = payment.getStatus().getKey();
        }
        BeanUtils.copyProperties(payment, this);
        if (!ObjectUtils.isEmpty(payment.getPayee())) {
            this.payee = new PayeeDto(payment.getPayee());
        }
        this.paymentId = payment.getUuid();
    }

    @Data
    @NoArgsConstructor
    public static class PayeeDto {
        private String firstName;
        private String lastName;
        private String businessName;
        private String email;
        private String phone;
        private String mailingAddress;
        private String mailingAddress2;
        private String mailingCity;
        private String mailingState;
        private String mailingZipCode;
        private boolean billingSameAsMailing;
        private String billingAddress;
        private String billingAddress2;
        private String billingCity;
        private String billingState;
        private String billingZipCode;

        public PayeeDto(Payee payee) {
            BeanUtils.copyProperties(payee, this);
            if (payee.isBillingSameAsMailing()) {
                this.billingAddress = payee.getMailingAddress();
                this.billingAddress2 = payee.getMailingAddress2();
                this.billingCity = payee.getMailingCity();
                this.billingState = payee.getMailingState();
                this.billingZipCode = payee.getMailingZipCode();
            }
        }
    }
}