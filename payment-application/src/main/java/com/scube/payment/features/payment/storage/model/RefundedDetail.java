package com.scube.payment.features.payment.storage.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.payment.features.payment.processing.dto.refund.RefundItemDto;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.util.UUID;

@Getter
@Setter
@Table(name = "refunded_detail")
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Audited
public class RefundedDetail extends AuditableEntity {
    @Size(max = 50)
    private String refundedParentTable;

    private UUID refundedParentId;
    private BigDecimal refundedAmount;

    @Transient
    private String primaryDisplay;


    @ManyToOne
    @JoinColumn(name = RefundTransaction.REFUND_TRANSACTION_ID)
    @JsonIgnore
    private RefundTransaction refundTransaction;

    public RefundedDetail(RefundItemDto request, RefundTransaction refundTransaction) {
        this.refundedParentTable = request.getParentTable();
        this.refundedParentId = request.getParentId();
        this.refundedAmount = request.getAmount();
        this.refundTransaction = refundTransaction;
    }
}
