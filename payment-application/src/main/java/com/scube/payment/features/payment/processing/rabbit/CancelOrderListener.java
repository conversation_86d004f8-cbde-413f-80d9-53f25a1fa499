package com.scube.payment.features.payment.processing.rabbit;

import java.util.UUID;

import org.springframework.stereotype.Component;

import com.scube.rabbit.core.fanout.subscriber.IRabbitFanoutSubscriber;

import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;


import com.scube.rabbit.core.fanout.subscriber.FanoutListener;
@Slf4j

@Component
@AllArgsConstructor

public class CancelOrderListener extends FanoutListener<CancelOrderListener.CancelOrderCommand> {

@Override
public void consume (CancelOrderCommand event){

log.info("The message");


}

    public  record CancelOrderCommand(UUID uuid) implements IRabbitFanoutSubscriber {}

}
