package com.scube.payment.features.payment.storage.model;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.audit.auditable.entity.AuditableEntity;
import com.scube.payment.features.payment.enums.RefundStatus;
import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentRequestDto;
import com.scube.payment.features.payment.storage.model.RefundedDetail;
import jakarta.persistence.*;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Getter;
import lombok.NoArgsConstructor;
import lombok.Setter;
import org.hibernate.envers.Audited;

import java.math.BigDecimal;
import java.math.RoundingMode;
import java.time.Instant;
import java.util.List;
import java.util.UUID;

@Getter
@Setter
@Table(name = "refund_transaction")
@NoArgsConstructor
@AllArgsConstructor
@Entity
@Audited
public class RefundTransaction extends AuditableEntity {
    public static final String REFUND_TRANSACTION_ID = "refund_transaction_id";


    @Size(max = 255)
    private String refundReference;

    private Instant transactionDate;
    private Instant voidedTs;
    private Instant refundedTs;

    @Enumerated(EnumType.STRING)
    private RefundStatus status = RefundStatus.PENDING;

    @Size(max = 255)
    private String paymentProvider;

    @Size(max = 500)
    private String refundReason;

    private Boolean isOnlineTransaction;
    private UUID orderId;
    private Long receiverId;


    @ManyToOne(cascade = CascadeType.ALL)
    @JoinColumn(name = "payment_id")
    private Payment payment;

    @OneToMany(cascade = CascadeType.ALL, mappedBy = "refundTransaction")
    private List<RefundedDetail> refundedDetail;


    public long getAmountInCents() {
        if (refundedDetail == null) {
            return 0;
        }
        return refundedDetail.stream()
                .map(item -> item.getRefundedAmount() != null
                        ? item.getRefundedAmount().multiply(BigDecimal.valueOf(100)).setScale(0, RoundingMode.HALF_UP)
                        : BigDecimal.ZERO)
                .mapToLong(BigDecimal::longValue)
                .sum();
    }

    public BigDecimal getRefundedTotal() {
        if (refundedDetail == null) {
            return BigDecimal.ZERO;
        }
        return refundedDetail.stream()
                .map(item -> item.getRefundedAmount() != null ? item.getRefundedAmount() : BigDecimal.ZERO)
                .reduce(BigDecimal.ZERO, BigDecimal::add);
    }

}
