package com.scube.payment.features.payment.processing.controller;

import com.scube.client.ServiceUrlConstant;
import com.scube.client.annotation.GenerateHttpExchange;
import com.scube.payment.features.payment.processing.dto.*;
import com.scube.payment.features.payment.processing.dto.refund.OrderInvoiceWithRefundTransactionsResponseDto;
import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentRequestDto;
import com.scube.payment.features.payment.processing.dto.refund.RefundPaymentResponseDto;
import com.scube.payment.features.payment.processing.service.PaymentProcessingService;
import com.scube.payment.features.payment.processing.service.RefundService;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.storage.model.RefundTransaction;
import com.scube.payment.features.payment.storage.service.PaymentStorageService;
import com.scube.payment.features.payment.validator.ValidPaymentAmount;
import com.scube.payment.features.permission.Permissions;
import com.scube.payment.features.providers.gateway.PaymentProviderGateway;
import com.scube.payment.features.providers.gateway.PaymentTokenRequest;
import com.scube.payment.features.providers.gateway.PaymentTokenResponse;
import jakarta.annotation.security.RolesAllowed;
import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.extern.slf4j.Slf4j;
import org.slf4j.MarkerFactory;
import org.springframework.validation.annotation.Validated;
import org.springframework.web.bind.annotation.*;

import java.time.LocalDate;
import java.util.List;
import java.util.UUID;

@AllArgsConstructor
@GenerateHttpExchange(value = ServiceUrlConstant.PAYMENT_SERVICE)
@RestController
@Slf4j
@Validated
public class PaymentController {
    private final PaymentStorageService paymentService;
    private final PaymentProviderGateway paymentProviderGateway;
    private final PaymentProcessingService paymentProcessingService;
    private final RefundService refundService;

    @GetMapping("/payments")
    @RolesAllowed(Permissions.Payment.GET_ALL_PAYMENTS)
    public List<Payment> getAllPayments() {
        return paymentService.getPaymentResponseDtos();
    }

    @GetMapping("/order/{orderId}")
    @RolesAllowed(Permissions.Payment.GET_PAYMENTS_BY_ORDER_ID)
    public List<GetPaymentResponseDto> getPaymentsByOrderId(@PathVariable UUID orderId) {
        return paymentService.getPaymentResponseDtos(orderId);
    }

    @GetMapping("/order/orderfilter")
    @RolesAllowed(Permissions.Payment.GET_PAYMENTS_BY_ORDER_ID)
    public List<GetPaymentResponseDto> filterPayments(
            @RequestParam(required = false) @Size(max = 255) String firstName,
            @RequestParam(required = false) @Size(max = 255) String lastName) {
        return paymentService.filterPayments(firstName, lastName);
    }

    @PostMapping("/process")
    @RolesAllowed(Permissions.Payment.SUBMIT_PAYMENT)
    public SubmitPaymentResponseDto submitPayment(@ValidPaymentAmount @RequestBody SubmitPaymentRequestDto paymentRequest) {
        return paymentProcessingService.submitPayment(paymentRequest);
    }

    @PostMapping("/refund")
    @RolesAllowed(Permissions.Payment.REFUND_PAYMENT)
    public RefundPaymentResponseDto refund(@RequestBody RefundPaymentRequestDto refundRequest) {
        return refundService.refund(refundRequest);
    }

    @GetMapping("/refundTransactions")
    @RolesAllowed(Permissions.Payment.GET_ALL_REFUND_TRANSACTIONS)
    public PaginatedResponse<RefundTransaction> getRefundTransactions(@RequestParam(value = "startDate", required = false) LocalDate startDate,
                                                                    @RequestParam(value = "endDate", required = false) LocalDate endDate,
                                                                      @RequestParam(required = false) @Size(max = 255) String status,
                                                                    @RequestParam(required = false, defaultValue = "1") int pageNumber,
                                                                    @RequestParam(required = false, defaultValue = "10") int pageSize)  {
        return refundService.getRefundTransaction(pageNumber, pageSize, status, startDate, endDate);
    }

    @GetMapping("/orderDetails/{orderId}")
    @RolesAllowed(Permissions.Payment.GET_REFUND_TRANSACTIONS_BY_ORDER_ID)
    public OrderInvoiceWithRefundTransactionsResponseDto getOrderWithRefundsByOrderId(@PathVariable UUID orderId) {
        return refundService.getRefundTransactionsByOrderId(orderId);
    }

    @PostMapping("/payments/token")
    @RolesAllowed(Permissions.Payment.GET_PAYMENT_TOKEN)
    public PaymentTokenResponse getPaymentToken(@ValidPaymentAmount @RequestBody PaymentTokenRequest req) {
        return paymentProviderGateway.getToken(req);
    }

    @DeleteMapping("/payments/{id}")
    @RolesAllowed(Permissions.Payment.DELETE_PAYMENT)
    public void deletePayment(@PathVariable("id") int id) {
        paymentService.deletePayment(id);
    }

    @PostMapping("/test/log")
    @RolesAllowed(Permissions.Payment.TEST_LOG)
    public void testLog() {
        log.error(MarkerFactory.getMarker("CRITICAL"),
                "Failed to process webhook " + UUID.randomUUID(), new Exception("Test exception"));
    }
}
