package com.scube.payment.features.payment.processing.dto.refund;

import com.scube.payment.features.payment.enums.RefundStatus;
import lombok.AllArgsConstructor;
import lombok.Data;
import lombok.NoArgsConstructor;

import java.math.BigDecimal;
import java.util.UUID;

@Data
@AllArgsConstructor
@NoArgsConstructor
public class RefundPaymentResponseDto {

    private UUID paymentId;
    private UUID orderId;
    private BigDecimal RefundedAmount;
    private RefundStatus status;
    private String refundReason;

}
