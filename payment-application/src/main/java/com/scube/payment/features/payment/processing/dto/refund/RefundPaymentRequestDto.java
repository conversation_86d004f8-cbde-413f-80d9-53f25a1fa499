package com.scube.payment.features.payment.processing.dto.refund;

import com.fasterxml.jackson.annotation.JsonIgnore;
import com.scube.payment.features.payment.storage.model.Payment;
import com.scube.payment.features.payment.processing.dto.refund.RefundItemDto;
import lombok.Data;

import java.math.BigDecimal;
import java.util.List;
import java.util.UUID;

@Data
public class RefundPaymentRequestDto {
    private UUID orderId;
    private UUID paymentId;
    private String refundReason;
    private List<RefundItemDto> refundItems;

    @JsonIgnore
    private Payment payment;


}
