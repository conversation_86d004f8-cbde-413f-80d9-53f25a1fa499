package com.scube.payment.features.payment.processing.dto;

import jakarta.validation.constraints.Size;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@AllArgsConstructor
@NoArgsConstructor
@Builder
@Data
public class OrderFilterRequestDto {
    @Size(max = 255)
    private String firstName;

    @Size(max = 255)
    private String lastName;
}
