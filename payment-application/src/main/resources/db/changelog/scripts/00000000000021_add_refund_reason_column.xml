<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                                       http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="addRefundReasonColumn" author="claude">
        <!-- Add the refund_reason column to refund_transaction table -->
        <addColumn tableName="refund_transaction">
            <column name="refund_reason" type="VARCHAR(500)"/>
        </addColumn>
    </changeSet>

    <changeSet id="addRefundReasonColumnToAuditTable" author="claude">
        <!-- Add the refund_reason column to audit_log_refund_transaction table -->
        <addColumn tableName="audit_log_refund_transaction">
            <column name="refund_reason" type="VARCHAR(500)"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
