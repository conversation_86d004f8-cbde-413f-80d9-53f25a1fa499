<?xml version="1.1" encoding="UTF-8" standalone="no"?>
<databaseChangeLog xmlns="http://www.liquibase.org/xml/ns/dbchangelog"
                   xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"
                   xsi:schemaLocation="http://www.liquibase.org/xml/ns/dbchangelog
                                       http://www.liquibase.org/xml/ns/dbchangelog/dbchangelog-latest.xsd">

    <changeSet id="add-order-number-column-to-payment" author="sathish">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="payment" columnName="order_number"/>
            </not>
        </preConditions>
        <!-- Add the order_number column to payment table -->
        <addColumn tableName="payment">
            <column name="order_number" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

    <changeSet id="add-order-number-column-to-audit-log-payment" author="sathish">
        <preConditions onFail="MARK_RAN">
            <not>
                <columnExists tableName="audit_log_payment" columnName="order_number"/>
            </not>
        </preConditions>
        <!-- Add the order_number column to audit_log_payment table -->
        <addColumn tableName="audit_log_payment">
            <column name="order_number" type="VARCHAR(255)"/>
        </addColumn>
    </changeSet>

</databaseChangeLog>
