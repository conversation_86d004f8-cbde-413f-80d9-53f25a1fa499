"use client";
import { useQuery, useMutation } from "@tanstack/react-query";
import { requests } from "@/utils/agent";
import { useMyProfile } from "@/hooks/providers/useMyProfile";

const payment = "/calculation/payments";

export interface PaymentSummary {
	id: string;
	orderId?: string;
	paymentId?: string;
	amount?: number;
	status?: string;
	paymentType?: string;
	transactionDate?: string;
	createdDateTime?: string;
	updatedDateTime?: string;
	[p: string]: any;
}

export interface RefundOrder {
  orderId: string;
  orderNumber: string;
  status: string;
  createdDate: string;
  updatedDate: string;
  itemNames: string[];
  userId: string;
}

export interface RefundOrderResponse {
  items: RefundOrder[];
  totalElements?: number;
  totalPages?: number;
  totalCount?: number;
  total?: number;
  count?: number;
  pageNumber?: number;
  pageSize?: number;
}

export interface PaymentOrderFilterResponse {
  paymentId: string;
  orderId: string;
  orderNumber?: string | null;
  transactionId: string;
  transactionDate: string;
  status: string;
  paymentType: string;
  amount: number;
  paymentProvider?: string | null;
  receiptId?: string | null;
  payee: {
    firstName: string;
    lastName: string;
    businessName: string;
    email: string;
    phone: string;
    mailingAddress: string;
    mailingAddress2: string;
  };
}


export const useGetAllPayments = () => {
	return useQuery<PaymentSummary[]>({
		queryKey: ["allPayments"],
		queryFn: () => requests.get<PaymentSummary[]>("/payment/payments"),
	});
};

// export const useGetRefundTransactions = (pageNumber: number, pageSize: number) => {
//     return useQuery<any>({
//         queryKey: ["RefundTransactions", pageNumber, pageSize],
//         queryFn: () => requests.get<any>(`/payment/refundTransactions?pageNumber=${pageNumber}&pageSize=${pageSize}`),
//     });
// };

export const useGetRefundTransactions = (
  pageNumber: number,
  pageSize: number,
  startDate?: string,
  endDate?: string,
) => {
  return useQuery<any>({
    queryKey: [
      "RefundTransactions",
      pageNumber,
      pageSize,
      startDate ?? null,
      endDate ?? null,
    ],
    queryFn: () => {
      const params: string[] = [
        `pageNumber=${pageNumber}`,
        `pageSize=${pageSize}`,
      ];

      if (startDate && endDate) {
        const encodedStart = encodeURIComponent(startDate);
        const encodedEnd = encodeURIComponent(endDate);
        params.push(`startDate=${encodedStart}`);
        params.push(`endDate=${encodedEnd}`);
      }

      const url = `/payment/refundTransactions?${params.join("&")}`;
      return requests.get<any>(url);
    },
  });
};


export const useGetRefundOrders = (
  pageNumber: number,
  pageSize: number,
  startDate?: string,
  endDate?: string,
  orderNumber?: string,
) => {
  return useQuery<RefundOrderResponse>({
    queryKey: [
      "RefundableOrders",
      pageNumber,
      pageSize,
      startDate ?? null,
      endDate ?? null,
      orderNumber ?? null,
    ],
    queryFn: () => {
      const params: string[] = [
        `pageNumber=${pageNumber}`,
        `pageSize=${pageSize}`,
      ];

      if (startDate && endDate) {
        const encodedStart = encodeURIComponent(startDate);
        const encodedEnd = encodeURIComponent(endDate);
        params.push(`startDate=${encodedStart}`);
        params.push(`endDate=${encodedEnd}`);
      }

      if (orderNumber) {
        params.push(`orderNumber=${encodeURIComponent(orderNumber)}`);
      }

      const url = `/calculation/order/refundable?${params.join("&")}`;
      return requests.get<RefundOrderResponse>(url);
    },
  });
};

export const useGetRefundOrdersByName = (
  pageNumber: number,
  pageSize: number,
  startDate?: string,
  endDate?: string,
  firstName?: string,
  lastName?: string,
) => {
  return useQuery<PaymentOrderFilterResponse[]>({
    queryKey: [
      "RefundableOrdersByName",
      pageNumber,
      pageSize,
      startDate ?? null,
      endDate ?? null,
      firstName ?? null,
      lastName ?? null,
    ],
    queryFn: async () => {
      const params: string[] = [];

      if (firstName) {
        params.push(`firstName=${encodeURIComponent(firstName)}`);
      }

      if (lastName) {
        params.push(`lastName=${encodeURIComponent(lastName)}`);
      }

      if (startDate && endDate) {
        const encodedStart = encodeURIComponent(startDate);
        const encodedEnd = encodeURIComponent(endDate);
        params.push(`startDate=${encodedStart}`);
        params.push(`endDate=${encodedEnd}`);
      }

      const url = `/payment/order/orderfilter${params.length > 0 ? '?' + params.join("&") : ''}`;
      return requests.get<PaymentOrderFilterResponse[]>(url);
    },
  });
};




export interface OrderDetails {
	id: string;
	[p: string]: any;
}

export const useGetOrderDetails = (orderId: string | null) => {
	return useQuery<OrderDetails>({
		queryKey: ["payments", "orderDetails", orderId],
		queryFn: () => requests.get<OrderDetails>(`/payment/orderDetails/${orderId}`),
		enabled: !!orderId,
	});
};

// Alternate endpoint matching provided example: /api/payment/order/{id}
export const useGetOrderDetailsByOrderId = (orderId: string | null) => {
	return useQuery<OrderDetails>({
		queryKey: ["payments", "order", orderId],
		queryFn: () => requests.get<OrderDetails>(`/payment/orderDetails/${orderId}`),
		enabled: !!orderId,
	});
};

type ReceiptData = {
	orderId: string;
	receipts: {
		paymentId: string;
		transactionDate: string;
		receiptUrl: string;
	}[];
};

// Process payment, send order and amount applied.
export const useProcessPayment = (
	orderId: string,
	amount: string,
	paymentType: string,
) => {
	return useMutation(() =>
		requests.post<any>(payment + `/${orderId}/create`, { amount, paymentType }),
	);
};

export const useStripeProcessPayment = (
	orderId: string,
	amount: string,
	paymentType: string,
) => {
	return useMutation(() =>
		requests.post<any>(payment + `/${orderId}/create`, { amount, paymentType }),
	);
}

export const useGetStripeKey = () => {
	// Only use on admin when ready for admin's to take payments through there system. -- Sean B
	return useQuery({
		queryKey: ["stripeKey"],
		queryFn: () =>  requests.get<any>(`/config/app-property/payment-providers.stripe.publishable-key`)
	});
}

// Cancel/Void Payment by payment ID
export const useCancelPayment = (paymentId: string) => {
	return useMutation(() =>
		requests.put<any>(payment + `/${paymentId}/cancel`, {}),
	);
};

// Get all voided payments by date
export const useGetAllVoidedPayments = (
	startDate: Date,
	endDate: Date,
	limit: number,
	page: number,
) => {
	return useQuery({
		queryKey: ["voidedPayments", startDate, endDate, limit, page],
		queryFn: () =>
			requests.get<any>(
				payment +
					`/voided?dateFrom=${startDate}&dateTo=${endDate}&limit=${limit}&page=${page}`,
			),
	});
};

// Get Receipt
export const useGetReceipt = (orderId: string, options?: any) => {
	const { hasPermissions } = useMyProfile();
	const permitted = hasPermissions(["super-admin"]);

	return useQuery<ReceiptData>({
		queryKey: ["receipt", orderId],
		queryFn: () =>
			permitted
				? requests.get(`/coordinator/payment/${orderId}/receipts`)
				: requests.get(`/coordinator/me/payment/${orderId}/receipts`),
		...options,
		enabled: !!orderId,
	});
};

// Refund an order (partial or full)
export const useRefundOrder = () => {
  return useMutation((payload: { orderId: string; paymentId: string; refundItems: any[] }) =>
    requests.post<any>(`/payment/refund`, payload),
  );
};

interface FeeEvent {
  uuid: string;
  eventTypeId: number;
  code: string;
  name: string;
  description: string;
  createdBy: string;
  createdDate: string;
  comment: string | null;
  action: string;
}

interface Association {
  entityType: string;
  entityId: string;
}

export interface Fee {
  entityId: string;
  entityType: string;
  events: FeeEvent[];
  feeCode: string;
  feeName: string;
  feeStatus: string;
  feeAmount: number;
  feePaidDate: string | null;
  orderId: string | null;
  comment: string;
  createdDateTime: string;
  updatedDateTime: string;
  createdBy: string;
  updatedBy: string;
  associations: Association[];
}

// Add Fee to Entity
export const useAddFee = () => {

  return useMutation(
    ({
      body,
    }: {
      body: {
        feeCode: string;
        amount: number;
        comment: string;
        associations: {
          entityId: string | null;
          entityType: string | null;
        }[];
      };
    }) =>
      requests.post<Fee>(
        `/license/entity-fee`,
        body,
      ),
  );
};