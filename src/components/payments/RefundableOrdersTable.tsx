"use client";
import React, { useState } from "react";
import {
  Table,
  TableBody,
  TableCell,
  TableHead,
  TableHeader,
  TableRow,
} from "@/components/ui/table";
import { Button } from "@/components/ui/button";
import { Calendar } from "@/components/ui/calendar";
import { CalendarIcon } from "lucide-react";
import { format } from "date-fns";
import { Badge } from "@/components/ui/badge";
import {
    Popover,
    PopoverContent,
    PopoverTrigger,
} from "@/components/ui/popover";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select";
import { useGetRefundOrders, useGetRefundOrdersByName, PaymentOrderFilterResponse } from "@/hooks/api/usePayment";
import PaginationLayout from "@/app/(app)/(realm)/(protected)/(app)/license/components/PaginationLayout";
import { FiExternalLink, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, FiX } from "react-icons/fi";
import { <PERSON>, <PERSON><PERSON>ontent, <PERSON><PERSON><PERSON><PERSON>, Card<PERSON>itle } from "@/components/ui/card";
export interface RefundableOrdersTableProps {
  onView?: (orderId: string) => void;
  onRefund?: (orderId: string) => void;
  onRowClick?: (orderId: string) => void;
}

function formatDate(dateString: string) {
  if (!dateString) return "";
  const date = new Date(dateString);
  const day = String(date.getDate()).padStart(2, "0");
  const month = String(date.getMonth() + 1).padStart(2, "0");
  const year = date.getFullYear();
  return `${day}-${month}-${year}`;
}

function getStatusBadge(status: string) {
  const base =
    "inline-flex items-center px-2 py-1 rounded-md text-xs font-semibold whitespace-nowrap border";

  if (status === "orderPaid") {
    return `${base} bg-emerald-200 text-emerald-900 border-emerald-300 
            dark:bg-emerald-800 dark:text-emerald-100 dark:border-emerald-700`;
  }

  if (status === "orderPartiallyRefunded") {
    return `${base} bg-amber-200 text-amber-900 border-amber-300 
            dark:bg-amber-800 dark:text-amber-100 dark:border-amber-700`;
  }

  return `${base} bg-gray-200 text-gray-900 border-gray-300 
          dark:bg-gray-700 dark:text-gray-100 dark:border-gray-600`;
}

export function RefundableOrdersTable({
  onView,
  onRefund,
  onRowClick,
}: RefundableOrdersTableProps) {
  // page.tsx style state
  const [page, setPage] = useState("0");
  const [size, setSize] = useState("10");
  const [isFiltersVisible, setIsFiltersVisible] = useState(false);

  // Search mode toggle
  const [searchMode, setSearchMode] = useState<"orderNumber" | "name">("orderNumber");

  // Separate state for UI inputs (what user types)
  const [startDateInput, setStartDateInput] = useState<string>("");
  const [endDateInput, setEndDateInput] = useState<string>("");
  const [orderNumberInput, setOrderNumberInput] = useState<string>("");
  const [firstNameInput, setFirstNameInput] = useState<string>("");
  const [lastNameInput, setLastNameInput] = useState<string>("");

  // Applied filters (what gets sent to API)
  const [appliedStartDate, setAppliedStartDate] = useState<string>("");
  const [appliedEndDate, setAppliedEndDate] = useState<string>("");
  const [appliedOrderNumber, setAppliedOrderNumber] = useState<string>("");
  const [appliedFirstName, setAppliedFirstName] = useState<string>("");
  const [appliedLastName, setAppliedLastName] = useState<string>("");

  const pageSize = parseInt(size);

  function formatToDMY(dateStr: string) {
    if (!dateStr) return "";
    const [y, m, d] = dateStr.split("-");
    return `${d}/${m}/${y}`; // DD/MM/YYYY
  }

  // Only use applied filters for API call - conditionally use the correct hook
  const orderNumberQuery = useGetRefundOrders(
    parseInt(page) + 1,
    pageSize,
    appliedStartDate ? formatToDMY(appliedStartDate) : undefined,
    appliedEndDate ? formatToDMY(appliedEndDate) : undefined,
    appliedOrderNumber || undefined,
  );

  const nameQuery = useGetRefundOrdersByName(
    parseInt(page) + 1,
    pageSize,
    appliedStartDate ? formatToDMY(appliedStartDate) : undefined,
    appliedEndDate ? formatToDMY(appliedEndDate) : undefined,
    appliedFirstName || undefined,
    appliedLastName || undefined,
  );

  // Helper function to transform payment data to order format
  const transformPaymentToOrder = (payment: PaymentOrderFilterResponse) => ({
    orderId: payment.orderId,
    orderNumber: payment.orderNumber || payment.orderId.substring(0, 8), // Use first 8 chars of orderId if orderNumber is null
    status: payment.status === "success" ? "orderPaid" : payment.status,
    createdDate: payment.transactionDate,
    updatedDate: payment.transactionDate,
    itemNames: [],
    userId: "",
  });

  // Use the appropriate query based on search mode
  const orderNumberQueryData = orderNumberQuery.data;
  const nameQueryData = nameQuery.data;

  // Get data from the active query
  const isLoading = searchMode === "orderNumber" ? orderNumberQuery.isLoading : nameQuery.isLoading;
  const isFetching = searchMode === "orderNumber" ? orderNumberQuery.isFetching : nameQuery.isFetching;
  const error = searchMode === "orderNumber" ? orderNumberQuery.error : nameQuery.error;

  // Handle different response structures
  let refundableOrders: any[] = [];
  let totalCount = 0;

  if (searchMode === "orderNumber") {
    const items = orderNumberQueryData?.items ?? [];
    // Ensure orderNumber is never null/undefined
    refundableOrders = items.map(item => ({
      ...item,
      orderNumber: item.orderNumber || item.orderId?.substring(0, 8) || 'N/A'
    }));
    totalCount =
      (orderNumberQueryData?.totalElements as number) ??
      (orderNumberQueryData?.totalCount as number) ??
      (orderNumberQueryData?.total as number) ??
      (orderNumberQueryData?.count as number) ??
      refundableOrders.length;
  } else {
    // Transform payment data to order format
    const paymentData = (nameQueryData ?? []) as PaymentOrderFilterResponse[];
    refundableOrders = paymentData.map(transformPaymentToOrder);
    totalCount = refundableOrders.length;
  }

  const totalPages = Math.ceil(totalCount / pageSize) || 1;

  // No client-side filtering needed since server handles it
  const filteredOrders = refundableOrders;

  // DatePicker subcomponent - only updates input state, not API calls
  function DatePicker({ value, onChange, label }: { value: string; onChange: (value: string | null) => void; label: string }) {
    const [date, setDate] = useState(value ? new Date(value) : null);
    return (
      <div className="flex flex-col gap-2">
        <label className="text-sm font-medium text-gray-700">{label}</label>
        <Popover>
          <PopoverTrigger asChild>
            <Button variant="outline" className="w-full justify-start text-left font-normal">
              {date ? (
                format(date, "MMM d, yyyy")
              ) : (
                <span className="text-gray-400">Select a date</span>
              )}
              <CalendarIcon className="ml-auto h-4 w-4 text-gray-500" />
            </Button>
          </PopoverTrigger>
          <PopoverContent className="w-auto p-0">
            <Calendar
              mode="single"
              selected={date ?? undefined}
              onSelect={(newDate) => {
                setDate(newDate ?? null);
                onChange(newDate ? format(newDate, "yyyy-MM-dd") : null);
              }}
              initialFocus
            />
          </PopoverContent>
        </Popover>
      </div>
    );
  }

  return (
    <div className="flex h-full flex-col overflow-auto overflow-x-hidden bg-gray-50 p-6">
      <Card className="border-0 shadow-sm">
      <CardHeader className="px-6 pb-0 pt-6">
  <div className="flex flex-col gap-4">
    {/* Title and Actions Row */}
    <div className="flex flex-col justify-between gap-4 sm:flex-row sm:items-center">
      <CardTitle className="text-xl">List of orders</CardTitle>
      <div className="flex items-center gap-3">
        <Button
          variant="outline"
          size="sm"
          onClick={() => setIsFiltersVisible(!isFiltersVisible)}
          className="flex items-center gap-1"
        >
          <FiFilter className="h-4 w-4" />
          Filters
          {([appliedStartDate, appliedEndDate, appliedOrderNumber, appliedFirstName, appliedLastName].filter(Boolean).length > 0) && (
            <Badge variant="secondary" className="ml-1 px-1.5 py-0 text-xs">
              {[appliedStartDate, appliedEndDate, appliedOrderNumber, appliedFirstName, appliedLastName].filter(Boolean).length}
            </Badge>
          )}
        </Button>
        <Select value={size} onValueChange={(value) => { setSize(value); setPage("0"); }}>
          <SelectTrigger className="w-[130px]">
            <SelectValue placeholder={`${size} per page`} />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="10">10 per page</SelectItem>
            <SelectItem value="25">25 per page</SelectItem>
            <SelectItem value="50">50 per page</SelectItem>
          </SelectContent>
        </Select>
      </div>
    </div>

    {/* Search Box Row with Integrated Toggle */}
    <div className="flex flex-col gap-3">
      {/* Segmented Control Toggle */}
      <div className="inline-flex w-fit rounded-lg bg-gray-100 p-1">
        <button
          onClick={() => {
            setSearchMode("orderNumber");
            // Clear name inputs when switching to order number mode
            setFirstNameInput("");
            setLastNameInput("");
            setAppliedFirstName("");
            setAppliedLastName("");
          }}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
            searchMode === "orderNumber"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
        >
          Order Number
        </button>
        <button
          onClick={() => {
            setSearchMode("name");
            // Clear order number input when switching to name mode
            setOrderNumberInput("");
            setAppliedOrderNumber("");
          }}
          className={`px-4 py-2 rounded-md text-sm font-medium transition-all ${
            searchMode === "name"
              ? "bg-white text-gray-900 shadow-sm"
              : "text-gray-600 hover:text-gray-900"
          }`}
        >
          Customer Name
        </button>
      </div>

      {/* Search Inputs */}
      <div className="flex flex-col gap-3 sm:flex-row sm:items-end">
        {searchMode === "orderNumber" ? (
          <div className="flex-1 sm:max-w-md">
            <div className="relative">
              <FiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
              <input
                type="text"
                value={orderNumberInput}
                onChange={(e) => setOrderNumberInput(e.target.value)}
                placeholder="Search by order number"
                className="w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
              />
            </div>
          </div>
        ) : (
          <div className="flex flex-1 gap-3">
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={firstNameInput}
                  onChange={(e) => setFirstNameInput(e.target.value)}
                  placeholder="First name"
                  className="w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>
            <div className="flex-1">
              <div className="relative">
                <FiSearch className="absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-gray-400" />
                <input
                  type="text"
                  value={lastNameInput}
                  onChange={(e) => setLastNameInput(e.target.value)}
                  placeholder="Last name"
                  className="w-full rounded-md border border-gray-300 py-2 pl-10 pr-3 text-sm focus:border-blue-500 focus:outline-none focus:ring-1 focus:ring-blue-500"
                />
              </div>
            </div>
          </div>
        )}
        <Button
          onClick={() => {
            setAppliedStartDate(startDateInput);
            setAppliedEndDate(endDateInput);
            if (searchMode === "orderNumber") {
              setAppliedOrderNumber(orderNumberInput);
              setAppliedFirstName("");
              setAppliedLastName("");
            } else {
              setAppliedFirstName(firstNameInput);
              setAppliedLastName(lastNameInput);
              setAppliedOrderNumber("");
            }
            setPage("0");
          }}
          disabled={isFetching}
          className="w-full sm:w-auto"
        >
          Search
        </Button>
      </div>
    </div>
  </div>
</CardHeader>
        <CardContent className="px-6 py-4">
          {isLoading || isFetching ? (
            <div className="space-y-2">
              <div className="p-8 text-center text-gray-500">Loading refundable orders…</div>
            </div>
          ) : error ? (
            <div className="p-8 text-center">
              <div className="text-gray-500">Error loading refundable orders</div>
              <Button variant="outline" onClick={() => { /* noop - hook refetches on state changes */ }} className="mt-4">
                Try Again
              </Button>
            </div>
          ) : filteredOrders.length === 0 ? (
            <div className="p-8 text-center">
              <div className="text-gray-500">No refundable orders found</div>
              <Button
                variant="outline"
                onClick={() => {
                  setStartDateInput("");
                  setEndDateInput("");
                  setOrderNumberInput("");
                  setFirstNameInput("");
                  setLastNameInput("");
                  setAppliedStartDate("");
                  setAppliedEndDate("");
                  setAppliedOrderNumber("");
                  setAppliedFirstName("");
                  setAppliedLastName("");
                  setPage("0");
                }}
                className="mt-4"
              >
                Clear Filters
              </Button>
            </div>
          ) : (
            <>
              {isFiltersVisible && (
                <div className="mb-6 rounded-md border border-gray-200 bg-gray-50 p-4">
                  <div className="mb-4 flex items-center justify-between">
                    <h3 className="font-medium">Filter Options</h3>
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={() => {
                        setStartDateInput("");
                        setEndDateInput("");
                        setOrderNumberInput("");
                        setFirstNameInput("");
                        setLastNameInput("");
                        setAppliedStartDate("");
                        setAppliedEndDate("");
                        setAppliedOrderNumber("");
                        setAppliedFirstName("");
                        setAppliedLastName("");
                        setPage("0");
                      }}
                      className="text-sm text-gray-500"
                    >
                      Clear All
                    </Button>
                  </div>
                  <div className="grid grid-cols-1 gap-4 md:grid-cols-3">
                   
                    <div className="space-y-2">
                      <DatePicker value={startDateInput} onChange={(v) => setStartDateInput(v ?? "")} label="Start Date" />
                    </div>
                    <div className="space-y-2">
                      <DatePicker value={endDateInput} onChange={(v) => setEndDateInput(v ?? "")} label="End Date" />
                    </div>
                  </div>
                  <div className="mt-4 flex justify-end">
                    <Button
                      onClick={() => {
                        setAppliedStartDate(startDateInput);
                        setAppliedEndDate(endDateInput);
                        if (searchMode === "orderNumber") {
                          setAppliedOrderNumber(orderNumberInput);
                          setAppliedFirstName("");
                          setAppliedLastName("");
                        } else {
                          setAppliedFirstName(firstNameInput);
                          setAppliedLastName(lastNameInput);
                          setAppliedOrderNumber("");
                        }
                        setPage("0");
                      }}
                      disabled={isFetching}
                    >
                      Apply Filters
                    </Button>
                  </div>
                </div>
              )}

              <div className="overflow-x-auto rounded-md border border-gray-200">
                <Table className="w-full min-w-[800px]">
                  <TableHeader>
                    <TableRow className="bg-gray-50">
                      <TableHead className="h-12 px-6 text-left font-bold text-gray-900 text-sm min-w-[160px]">Order Number</TableHead>
                      <TableHead className="h-12 px-6 text-left font-bold text-gray-900 text-sm min-w-[120px]">Status</TableHead>
                      <TableHead className="h-12 px-6 text-left font-bold text-gray-900 text-sm min-w-[120px]">Finance Status</TableHead>
                      <TableHead className="h-12 px-6 text-left font-bold text-gray-900 text-sm min-w-[130px]">Purchased On</TableHead>
                    </TableRow>
                  </TableHeader>
                  <TableBody>
                    {filteredOrders.map((order: any, index: number) => (
                      <TableRow
                        key={order.orderId}
                        onClick={() => onRowClick?.(order.orderId)}
                        className={`border-b border-gray-100 transition-all duration-200 cursor-pointer hover:bg-blue-50 dark:hover:bg-blue-900/20 ${index % 2 === 0 ? 'bg-white' : 'bg-gray-50/60'}`}
                      >
                        <TableCell className="px-6 py-4">
                          <div className="font-semibold text-gray-900 text-sm">#{order.orderNumber}</div>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                          <span className={getStatusBadge(order.status)}>
                            {order.status}
                          </span>
                        </TableCell>
                        <TableCell className="px-6 py-4">
                                                  <span className={getStatusBadge(order.status)}>
                                                    {order.financeStatus}
                                                  </span>
                                                </TableCell>
                        <TableCell className="px-6 py-4">
                          <div className="text-sm text-gray-600 font-medium">{formatDate(order.createdDate)}</div>
                        </TableCell>
                      </TableRow>
                    ))}
                  </TableBody>
                </Table>
              </div>

              <div className="mt-4">
                <PaginationLayout
                  currentPage={page}
                  totalPages={totalPages}
                  back={() => setPage(Math.max(0, parseInt(page) - 1).toString())}
                  next={() => setPage(Math.min(totalPages - 1, parseInt(page) + 1).toString())}
                  pageSelect={(idx) => setPage(idx.toString())}
                />
              </div>
            </>
          )}
        </CardContent>
      </Card>
    </div>
  );
}

export default RefundableOrdersTable;