"use client";
import React from "react";
import { OrderItem } from "./OrderItemComponent";
import { OrderIcon, formatEpochSeconds, formatDateTime } from "./orderDetailsUtils";

type TabType = 'items' | 'payments' | 'refunds' | 'history';

interface OrderTabsContentProps {
  activeTab: TabType;
  setActiveTab: (tab: TabType) => void;
  visibleItems: any[];
  data: any;
  selectedItems: Set<number>;
  expandedItems: Set<number>;
  toggleItemSelection: (itemId: number) => void;
  toggleItemExpansion: (itemId: number) => void;
  selectedFees: Set<string>;
  toggleFeeSelection: (feeUuid: string) => void;
  updateItemPrice: (itemId: number, newPrice: number, originalPrice: number, itemLabel: string) => void;
  updateFeePrice: (feeUuid: string, newPrice: number, originalPrice: number, feeLabel: string) => void;
  toggleSelectAll: () => void;
  isAllSelected: boolean;
  order: any;
  itemRefunds: Map<number, number>;
  feeRefunds: Map<string, number>;
  tempItemRefunds: Map<number, { refundAmount: number, originalPrice: number, label: string, parentId: string }>;
  tempFeeRefunds: Map<string, { refundAmount: number, originalPrice: number, label: string }>;
  onOpenFeeRefundModal: (feeUuid: string) => void;
}

// Separate component for individual refund items to manage their own state
const RefundItem: React.FC<{ refund: any; index: number }> = ({ refund, index }) => {
  const [open, setOpen] = React.useState(false);

  return (
    <div
      key={refund.id || index}
      className="group border border-gray-200 dark:border-gray-700 rounded-xl overflow-hidden transition-all duration-300 hover:shadow-lg dark:hover:border-gray-600"
    >
      {/* Accordion Header */}
      <button
        onClick={() => setOpen(!open)}
        className="w-full p-5 bg-white dark:bg-gray-800 hover:bg-gray-50 dark:hover:bg-gray-750 transition-colors duration-200"
      >
        <div className="flex items-center justify-between">
          {/* Left Section */}
          <div className="flex items-center gap-4">
            <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-emerald-500 to-teal-600 flex items-center justify-center shadow-md flex-shrink-0">
              <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" d="M9 14l6-6m-5.5.5h.01m4.99 5h.01M19 21V5a2 2 0 00-2-2H7a2 2 0 00-2 2v16l3.5-2 3.5 2 3.5-2 3.5 2z" />
              </svg>
            </div>
            <div className="flex flex-col text-left">
              <div className="flex items-center gap-2 mb-1">
                <span className="text-xs font-medium text-gray-500 dark:text-gray-400 uppercase tracking-wide">
                  Refund ID
                </span>
                <span className="text-sm font-bold text-gray-900 dark:text-white">
                  #{refund.id}
                </span>
              </div>
              <div className="flex items-center gap-3 mb-1.5">
                <div className="flex items-baseline gap-1">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Amount:</span>
                  <span className="text-xl font-bold text-gray-900 dark:text-white">
                    ${refund.refundedTotal.toFixed(2)}
                  </span>
                </div>
                <span className="text-gray-300 dark:text-gray-600">|</span>
                <span className={`text-xs font-semibold px-2.5 py-1 rounded-full ${refund.status === 'COMPLETED'
                    ? 'bg-green-100 text-green-700 dark:bg-green-900/30 dark:text-green-400'
                    : refund.status === 'PENDING'
                      ? 'bg-yellow-100 text-yellow-700 dark:bg-yellow-900/30 dark:text-yellow-400'
                      : 'bg-gray-100 text-gray-700 dark:bg-gray-700 dark:text-gray-300'
                  }`}>
                  {refund.status}
                </span>
              </div>
              {refund.refundReason && (
                <div className="flex items-center gap-1.5">
                  <span className="text-xs text-gray-500 dark:text-gray-400">Reason:</span>
                  <span className="text-sm text-gray-700 dark:text-gray-300 font-medium truncate max-w-xs">
                    {refund.refundReason}
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Right Section - Expand Icon */}
          <div className="flex items-center gap-3">
            <div className="hidden sm:flex flex-col items-end text-xs text-gray-500 dark:text-gray-400">
              <span>{refund.createdDate ? new Date(refund.createdDate).toLocaleDateString() : 'N/A'}</span>
              <span className="text-xs">{(refund.refundedDetail ?? []).length} item(s)</span>
            </div>
            <svg
              className={`w-5 h-5 text-gray-400 transition-transform duration-300 flex-shrink-0 ${open ? 'rotate-180' : ''
                }`}
              fill="none"
              stroke="currentColor"
              strokeWidth="2"
              viewBox="0 0 24 24"
            >
              <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
            </svg>
          </div>
        </div>
      </button>

      {/* Accordion Body */}
      <div
        className={`transition-[max-height,opacity] duration-500 ease-in-out ${open ? 'max-h-screen opacity-100' : 'max-h-0 opacity-0'
          } overflow-hidden`}
      >
        <div className="p-4 sm:p-5 border-t border-gray-100 dark:border-gray-700 bg-white dark:bg-gray-900">

          {/* Refunded Items */}
          <div className="mb-4">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Refunded Items
            </p>
            <div className="space-y-2">
              {(refund.refundedDetail ?? []).length > 0 ? (
                refund.refundedDetail.map((detail: any, i: number) => (
                  <div
                    key={detail.id || i}
                    className="relative overflow-hidden rounded-lg border border-gray-200 dark:border-gray-700 bg-white dark:bg-gray-800 hover:border-blue-300 dark:hover:border-blue-600 transition-all duration-200 hover:shadow-md"
                  >
                    <div className="flex items-center p-4">
                      {/* Icon */}
                      <div className="flex-shrink-0 mr-4">
                        <div className="w-12 h-12 rounded-xl bg-gradient-to-br from-blue-500 to-indigo-600 flex items-center justify-center shadow-lg">
                          <svg className="w-6 h-6 text-white" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                            <path strokeLinecap="round" strokeLinejoin="round" d="M20 7l-8-4-8 4m16 0l-8 4m8-4v10l-8 4m0-10L4 7m8 4v10M4 7v10l8 4" />
                          </svg>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-start justify-between gap-3">
                          <div className="flex-1 min-w-0">
                            <h4 className="font-semibold text-gray-900 dark:text-white text-sm mb-1">
                              {detail.primaryDisplay}
                            </h4>
                            <div className="flex flex-wrap items-center gap-2 text-xs">
                              {detail.refundedParentTable && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded-md bg-gray-100 dark:bg-gray-700 text-gray-700 dark:text-gray-300 font-medium">
                                  <svg className="w-3 h-3 mr-1" fill="currentColor" viewBox="0 0 20 20">
                                    <path d="M9 2a1 1 0 000 2h2a1 1 0 100-2H9z" />
                                    <path fillRule="evenodd" d="M4 5a2 2 0 012-2 3 3 0 003 3h2a3 3 0 003-3 2 2 0 012 2v11a2 2 0 01-2 2H6a2 2 0 01-2-2V5zm3 4a1 1 0 000 2h.01a1 1 0 100-2H7zm3 0a1 1 0 000 2h3a1 1 0 100-2h-3zm-3 4a1 1 0 100 2h.01a1 1 0 100-2H7zm3 0a1 1 0 100 2h3a1 1 0 100-2h-3z" clipRule="evenodd" />
                                  </svg>
                                  {detail.refundedParentTable}
                                </span>
                              )}
                              {detail.secondaryDisplay && (
                                <span className="text-gray-500 dark:text-gray-400">
                                  {detail.secondaryDisplay}
                                </span>
                              )}
                              {detail.quantity && (
                                <span className="inline-flex items-center px-2 py-0.5 rounded-md bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 font-medium">
                                  Qty: {detail.quantity}
                                </span>
                              )}
                            </div>
                          </div>

                          {/* Amount */}
                          <div className="flex items-center gap-2 flex-shrink-0">
                            {detail.refundedAmount && (
                              <div className="text-right">
                                <p className="font-bold text-lg text-gray-900 dark:text-white">
                                  ${detail.refundedAmount.toFixed(2)}
                                </p>
                              </div>
                            )}
                            <div className="w-8 h-8 rounded-full bg-green-100 dark:bg-green-900/30 flex items-center justify-center">
                              <svg className="w-4 h-4 text-green-600 dark:text-green-400" fill="currentColor" viewBox="0 0 20 20">
                                <path fillRule="evenodd" d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z" clipRule="evenodd" />
                              </svg>
                            </div>
                          </div>
                        </div>
                      </div>
                    </div>
                  </div>
                ))
              ) : (
                <div className="text-center py-8 text-sm text-gray-500 dark:text-gray-400 bg-gray-50 dark:bg-gray-800 rounded-lg border-2 border-dashed border-gray-200 dark:border-gray-700">
                  No items found
                </div>
              )}
            </div>
          </div>

          {/* Timeline */}
          <div className="border-t border-gray-100 dark:border-gray-700 pt-4">
            <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
              Refund Timeline
            </p>
            <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Refund Started</p>
                <p className="font-semibold">
                  {refund.createdDate ? formatDateTime(refund.createdDate) : 'N/A'}
                </p>

              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Last Modified</p>
                <p className="font-semibold">
                  {refund.lastModifiedDate
                    ? formatDateTime(refund.lastModifiedDate)
                    : 'N/A'}
                </p>
              </div>
              <div>
                <p className="text-sm text-gray-500 dark:text-gray-400">Refund Completed</p>
                <p className="font-semibold">
                  {refund.refundedTs ? formatDateTime(refund.refundedTs) : 'N/A'}
                </p>
              </div>
            </div>
          </div>

          {/* Additional Info */}
          {(refund.createdBy || refund.lastModifiedBy) && (
            <div className="border-t border-gray-100 dark:border-gray-700 pt-4 mt-4">
              <p className="text-sm font-medium text-gray-700 dark:text-gray-300 mb-3">
                Additional Details
              </p>
              <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                {refund.createdBy && (
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">Initiated By</p>
                    <p className="font-semibold text-gray-900 dark:text-white">
                      {refund.createdBy}
                    </p>
                  </div>
                )}
                {refund.lastModifiedBy && (
                  <div>
                    <p className="text-sm text-gray-500 dark:text-gray-400">
                      Last Modified By
                    </p>
                    <p className="font-semibold text-gray-900 dark:text-white">
                      {refund.lastModifiedBy}
                    </p>
                  </div>
                )}
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
};

export const OrderTabsContent: React.FC<OrderTabsContentProps> = ({
  activeTab,
  setActiveTab,
  visibleItems,
  data,
  selectedItems,
  expandedItems,
  toggleItemSelection,
  toggleItemExpansion,
  selectedFees,
  toggleFeeSelection,
  updateItemPrice,
  updateFeePrice,
  toggleSelectAll,
  isAllSelected,
  order,
  itemRefunds,
  feeRefunds,
  tempItemRefunds,
  tempFeeRefunds,
  onOpenFeeRefundModal
}) => {
  return (
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700">
      <div className="flex overflow-x-auto border-b border-gray-200 dark:border-gray-700">
        {[
          { id: 'items', label: `Items (${visibleItems.length})` },
          { id: 'payments', label: `Payments (${(data as any)?.payments?.length ?? 0})` },
          { id: 'refunds', label: `Refunds (${(data as any)?.refundTransactions?.length ?? 0})` },
          { id: 'history', label: 'History' }
        ].map((tab) => (
          <button
            key={tab.id}
            onClick={() => setActiveTab(tab.id as TabType)}
            className={`flex-shrink-0 px-3 sm:px-6 py-2 sm:py-3 text-xs sm:text-sm font-medium border-b-2 transition-colors hover:no-underline focus:no-underline ${activeTab === tab.id
                ? 'text-blue-600 bg-blue-50 dark:bg-blue-900 dark:text-blue-400 border-blue-600'
                : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 border-transparent'
              }`}
          >
            {tab.label}
          </button>
        ))}
      </div>

      <div className="p-3 sm:p-6">
        {/* Items Tab */}
        {activeTab === 'items' && (
          <>
            <div className="flex flex-col sm:flex-row sm:items-center justify-between gap-2 sm:gap-0 mb-4">
              <div className="flex items-center space-x-2">
                <OrderIcon />
                <h2 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white">Order Items</h2>
              </div>
              <button
                onClick={toggleSelectAll}
                className="text-xs sm:text-sm text-blue-600 dark:text-blue-400 hover:text-blue-800 dark:hover:text-blue-300 self-start sm:self-auto"
              >
                {isAllSelected ? 'Deselect All' : 'Select All'}
              </button>
            </div>

            <div className="space-y-3 sm:space-y-4">
              {visibleItems.map((item: any) => {
                // Calculate total refunded from all refund transactions
                const totalRefunded = ((data as any)?.refundTransactions ?? []).reduce(
                  (sum: number, refund: any) => sum + (refund.refundedTotal || 0),
                  0
                );

                return (
                  <OrderItem
                    key={item.orderItemId}
                    item={item}
                    isSelected={selectedItems.has(item.orderItemId)}
                    isExpanded={expandedItems.has(item.orderItemId)}
                    onToggleSelect={() => toggleItemSelection(item.orderItemId)}
                    onToggleExpand={() => toggleItemExpansion(item.orderItemId)}
                    selectedFees={selectedFees}
                    onToggleFee={toggleFeeSelection}
                    onUpdateItemPrice={updateItemPrice}
                    onUpdateFeePrice={updateFeePrice}
                    refundedAmount={itemRefunds.get(item.orderItemId) || 0}
                    feeRefunds={feeRefunds}
                    pendingRefundAmount={tempItemRefunds.get(item.orderItemId)?.refundAmount || 0}
                    tempFeeRefunds={tempFeeRefunds}
                    tempItemRefunds={tempItemRefunds}
                    orderTotal={order?.total || 0}
                    orderTotalRefunded={totalRefunded}
                    onOpenFeeRefundModal={onOpenFeeRefundModal}
                  />
                );
              })}
            </div>
          </>
        )}

        {/* Payments Tab */}
        {activeTab === 'payments' && (
          <div>
            <h2 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-3 sm:mb-4">Payment Information</h2>
            {((data as any)?.payments ?? []).map((payment: any) => (
              <div key={payment.paymentId} className="border border-gray-200 dark:border-gray-700 rounded-lg p-3 sm:p-4 mb-3 sm:mb-4">
                {/* Payment Details Section */}
                <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4 mb-4">
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Payment Number</p>
                    <p className="font-semibold text-gray-900 dark:text-white">{payment.paymentNumber || payment.paymentId}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Amount</p>
                    <p className="font-semibold text-gray-900 dark:text-white">${payment.amount.toFixed(2)}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Payment Type</p>
                    <p className="font-semibold text-gray-900 dark:text-white">{payment.paymentType}</p>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Status</p>
                    <span className="inline-flex items-center px-2 py-1 rounded-full text-xs font-medium bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200">
                      {payment.status}
                    </span>
                  </div>
                  <div>
                    <p className="text-sm text-gray-600 dark:text-gray-400">Transaction Date</p>
                    <p className="font-semibold text-gray-900 dark:text-white">{formatDateTime(payment.transactionDate)}</p>
                  </div>
                </div>

                {/* Payee Information Section */}
                {payment.payee && (
                  <div className="border-t border-gray-200 dark:border-gray-700 pt-4">
                    <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Payee Information</h3>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3 sm:gap-4">
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">First Name</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{payment.payee.firstName || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Last Name</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{payment.payee.lastName || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Email</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{payment.payee.email || 'N/A'}</p>
                      </div>
                      <div>
                        <p className="text-sm text-gray-600 dark:text-gray-400">Phone</p>
                        <p className="font-semibold text-gray-900 dark:text-white">{payment.payee.phone || 'N/A'}</p>
                      </div>
                      {payment.payee.businessName && (
                        <div>
                          <p className="text-sm text-gray-600 dark:text-gray-400">Business Name</p>
                          <p className="font-semibold text-gray-900 dark:text-white">{payment.payee.businessName}</p>
                        </div>
                      )}
                    </div>

                    {/* Address Information */}
                    {(payment.payee.mailingAddress || payment.payee.billingAddress) && (
                      <div className="mt-4 bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg border border-gray-200 dark:border-gray-600">
                        <h4 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Contact Address</h4>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-4">
                          {payment.payee.mailingAddress && (
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Mailing Address</p>
                              <div className="text-sm text-gray-900 dark:text-white">
                                <p>{payment.payee.mailingAddress}</p>
                                {payment.payee.mailingAddress2 && <p>{payment.payee.mailingAddress2}</p>}
                                <p>
                                  {payment.payee.mailingCity && `${payment.payee.mailingCity}, `}
                                  {payment.payee.mailingState && `${payment.payee.mailingState} `}
                                  {payment.payee.mailingZipCode}
                                </p>
                              </div>
                            </div>
                          )}
                          {payment.payee.billingAddress && !payment.payee.billingSameAsMailing && (
                            <div>
                              <p className="text-sm text-gray-600 dark:text-gray-400 mb-1">Billing Address</p>
                              <div className="text-sm text-gray-900 dark:text-white">
                                <p>{payment.payee.billingAddress}</p>
                                {payment.payee.billingAddress2 && <p>{payment.payee.billingAddress2}</p>}
                                <p>
                                  {payment.payee.billingCity && `${payment.payee.billingCity}, `}
                                  {payment.payee.billingState && `${payment.payee.billingState} `}
                                  {payment.payee.billingZipCode}
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>
                    )}
                  </div>
                )}
              </div>
            ))}
          </div>
        )}


        {/* Refunds Tab */}
        {activeTab === 'refunds' && (
          <div>
            <h2 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-4">
              Refund History
            </h2>

            {((data as any)?.refundTransactions ?? []).length === 0 ? (
              <p className="text-gray-500 dark:text-gray-400">No refunds found.</p>
            ) : (
              <div className="space-y-3">
                {((data as any)?.refundTransactions ?? []).map((refund: any, index: number) => (
                  <RefundItem key={refund.id || index} refund={refund} index={index} />
                ))}
              </div>
            )}
          </div>
        )}


        {/* History Tab */}
        {activeTab === 'history' && (
          <div>
            <h2 className="text-base sm:text-lg font-semibold text-gray-900 dark:text-white mb-6 sm:mb-8">Order History</h2>
            <div className="relative">
              {/* Vertical Timeline Line */}
              <div className="absolute left-[15px] sm:left-[19px] top-0 bottom-0 w-0.5 bg-gray-200 dark:bg-gray-700"></div>

              <div className="space-y-6 sm:space-y-8">
                {(() => {
                  // Collect all history events with timestamps
                  const historyEvents: Array<{
                    type: 'order' | 'payment' | 'refund';
                    timestamp: number;
                    data: any;
                  }> = [];

                  // Add order created event
                  if (order?.createdDate) {
                    historyEvents.push({
                      type: 'order',
                      timestamp: order.createdDate,
                      data: order
                    });
                  }

                  // Add payment events
                  ((data as any)?.payments ?? []).forEach((payment: any) => {
                    historyEvents.push({
                      type: 'payment',
                      timestamp: new Date(payment.transactionDate || payment.createdDate).getTime(),
                      data: payment
                    });
                  });

                  // Add refund events
                  ((data as any)?.refundTransactions ?? []).forEach((refund: any) => {
                    historyEvents.push({
                      type: 'refund',
                      timestamp: new Date(refund.refundedTs || refund.createdDate).getTime(),
                      data: refund
                    });
                  });

                  // Sort by timestamp in descending order (most recent first)
                  historyEvents.sort((a, b) => b.timestamp - a.timestamp);

                  // Render sorted events
                  return historyEvents.length > 0 ? (
                    historyEvents.map((event, index) => {
                      if (event.type === 'order') {
                        return (
                          <div key={`order-${index}`} className="relative flex items-start space-x-3 sm:space-x-4">
                            <div className="relative z-10 flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-blue-500 dark:bg-blue-600 rounded-full flex items-center justify-center ring-4 ring-white dark:ring-gray-900">
                              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 6v6m0 0v6m0-6h6m-6 0H6" />
                              </svg>
                            </div>
                            <div className="flex-1 min-w-0 pb-2">
                              <p className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white">Order Created</p>
                              <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-0.5">{formatEpochSeconds(event.data.createdDate / 1000)}</p>
                              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mt-1">
                                Order #{event.data.orderNumber} was created.
                              </p>
                            </div>
                          </div>
                        );
                      } else if (event.type === 'payment') {
                        const payment = event.data;
                        return (
                          <div key={`payment-${payment.paymentId}`} className="relative flex items-start space-x-3 sm:space-x-4">
                            <div className="relative z-10 flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-green-500 dark:bg-green-600 rounded-full flex items-center justify-center ring-4 ring-white dark:ring-gray-900">
                              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
                              </svg>
                            </div>
                            <div className="flex-1 min-w-0 pb-2">
                              <p className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white">Payment Received</p>
                              <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                                {formatDateTime(payment.transactionDate || payment.createdDate)}
                              </p>
                              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mt-1">
                                Payment of ${payment.amount.toFixed(2)} received via {payment.paymentType}.
                              </p>
                            </div>
                          </div>
                        );
                      } else {
                        const refund = event.data;
                        return (
                          <div key={`refund-${refund.id}`} className="relative flex items-start space-x-3 sm:space-x-4">
                            <div className="relative z-10 flex-shrink-0 w-8 h-8 sm:w-10 sm:h-10 bg-orange-500 dark:bg-orange-600 rounded-full flex items-center justify-center ring-4 ring-white dark:ring-gray-900">
                              <svg className="w-4 h-4 sm:w-5 sm:h-5 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h10a8 8 0 018 8v2M3 10l6 6m-6-6l6-6" />
                              </svg>
                            </div>
                            <div className="flex-1 min-w-0 pb-2">
                              <p className="text-sm sm:text-base font-semibold text-gray-900 dark:text-white">Refund Processed</p>
                              <p className="text-xs sm:text-sm text-gray-500 dark:text-gray-400 mt-0.5">
                                {formatDateTime(refund.refundedTs || refund.createdDate)}
                              </p>
                              <p className="text-xs sm:text-sm text-gray-600 dark:text-gray-300 mt-1">
                                Refund of ${refund.refundedTotal.toFixed(2)} processed (Status: {refund.status}).
                              </p>
                            </div>
                          </div>
                        );
                      }
                    })
                  ) : (
                    <div className="text-center py-8 sm:py-12">
                      <p className="text-sm text-gray-500 dark:text-gray-400">No history available</p>
                    </div>
                  );
                })()}
              </div>
            </div>
          </div>
        )}

      </div>
    </div>
  );
};