import React from "react";
import { FiAward, FiFile } from "react-icons/fi";

// Formatting functions
export const formatEpochSeconds = (epoch?: number) => {
  if (!epoch) return "-";
  try {
    return new Date(epoch * 1000).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    });
  } catch {
    return String(epoch);
  }
};

export const formatDateTime = (dateString?: string) => {
  if (!dateString) return "-";
  try {
    const date = new Date(dateString);
    return date.toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
      hour: "2-digit",
      minute: "2-digit",
      hour12: true
    });
  } catch {
    return dateString;
  }
};

export const formatOrderStatus = (status?: string) => {
  if (!status) return "Unknown";
  const formatted = status
    .replace(/([A-Z])/g, ' $1')
    .replace(/^./, str => str.toUpperCase())
    .trim();
  return formatted;
};

export const getStatusStyle = (status?: string) => {
  if (!status) return {
    bg: "bg-gray-50 dark:bg-gray-900/30",
    text: "text-gray-800 dark:text-gray-300",
    border: "border-gray-200 dark:border-gray-800"
  };

  const lowerStatus = status.toLowerCase();

  if (lowerStatus.includes("paid") && !lowerStatus.includes("refund")) {
    return {
      bg: "bg-green-50 dark:bg-green-900/30",
      text: "text-green-800 dark:text-green-300",
      border: "border-green-200 dark:border-green-800"
    };
  }

  if (lowerStatus.includes("partiallyrefunded") || lowerStatus.includes("partially")) {
    return {
      bg: "bg-orange-50 dark:bg-orange-900/30",
      text: "text-orange-800 dark:text-orange-300",
      border: "border-orange-200 dark:border-orange-800"
    };
  }

  if (lowerStatus.includes("refunded")) {
    return {
      bg: "bg-red-50 dark:bg-red-900/30",
      text: "text-red-800 dark:text-red-300",
      border: "border-red-200 dark:border-red-800"
    };
  }

  if (lowerStatus.includes("pending")) {
    return {
      bg: "bg-yellow-50 dark:bg-yellow-900/30",
      text: "text-yellow-800 dark:text-yellow-300",
      border: "border-yellow-200 dark:border-yellow-800"
    };
  }

  if (lowerStatus.includes("cancelled") || lowerStatus.includes("failed")) {
    return {
      bg: "bg-gray-50 dark:bg-gray-900/30",
      text: "text-gray-800 dark:text-gray-300",
      border: "border-gray-200 dark:border-gray-800"
    };
  }

  return {
    bg: "bg-blue-50 dark:bg-blue-900/30",
    text: "text-blue-800 dark:text-blue-300",
    border: "border-blue-200 dark:border-blue-800"
  };
};

// Icon Components
export const OrderIcon = ({ isDogTag }: { isDogTag?: boolean }) => (
  isDogTag
    ? <FiAward className="w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-gray-600 dark:text-gray-400" />
    : <FiFile className="w-5 h-5 sm:w-6 sm:h-6 lg:w-7 lg:h-7 text-gray-600 dark:text-gray-400" />
);

export const CalendarIcon = () => (
  <svg className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
    <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z" />
  </svg>
);

export const ChevronIcon = () => (
  <svg className="w-4 h-4 sm:w-5 sm:h-5 lg:w-6 lg:h-6 text-gray-600 dark:text-gray-400" fill="currentColor" viewBox="0 0 20 20">
    <path fillRule="evenodd" d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z" clipRule="evenodd" />
  </svg>
);