// "use client";
// import React from "react";
// import { Button } from "@/components/ui/button";

// interface RefundItem {
//   id: string;
//   label: string;
//   originalAmount: number;
//   newAmount: number;
//   refundAmount: number;
//   type: 'item' | 'fee';
// }

// interface RefundConfirmationModalProps {
//   isOpen: boolean;
//   onClose: () => void;
//   onConfirm: () => void;
//   refundItems: RefundItem[];
//   isLoading?: boolean;
// }

// export function RefundConfirmationModal({
//   isOpen,
//   onClose,
//   onConfirm,
//   refundItems,
//   isLoading = false
// }: RefundConfirmationModalProps) {
//   if (!isOpen) return null;

//   const totalOriginal = refundItems.reduce((sum, item) => sum + item.originalAmount, 0);
//   const totalNew = refundItems.reduce((sum, item) => sum + item.newAmount, 0);
//   const totalRefund = refundItems.reduce((sum, item) => sum + item.refundAmount, 0);

//   return (
//     <div className="fixed inset-0 z-[9999] bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
//       <div className="w-full max-w-2xl bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700">
//         {/* Header */}
//         <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
//           <div>
//             <h2 className="text-xl font-bold text-gray-900 dark:text-white">Confirm Refund</h2>
//             <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
//               Review the refund details before proceeding
//             </p>
//           </div>
//           <button
//             onClick={onClose}
//             disabled={isLoading}
//             className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
//           >
//             <svg className="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
//               <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
//             </svg>
//           </button>
//         </div>

//         {/* Content */}
//         <div className="max-h-[60vh] overflow-y-auto px-6 py-4">
//           {/* Info Banner */}
//           <div className="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
//             <div className="flex items-start gap-3">
//               <svg className="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5" fill="currentColor" viewBox="0 0 20 20">
//                 <path fillRule="evenodd" d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z" clipRule="evenodd" />
//               </svg>
//               <div className="flex-1">
//                 <h3 className="text-sm font-semibold text-yellow-800 dark:text-yellow-400">
//                   Refund Action Required
//                 </h3>
//                 <p className="text-sm text-yellow-700 dark:text-yellow-500 mt-1">
//                   The price has been reduced. A refund will be processed for the difference.
//                 </p>
//               </div>
//             </div>
//           </div>

//           {/* Refund Items */}
//           <div className="space-y-3 mb-4">
//             <h3 className="text-sm font-semibold text-gray-900 dark:text-white">Items to Refund:</h3>
//             {refundItems.map((item) => (
//               <div
//                 key={item.id}
//                 className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
//               >
//                 <div className="flex items-start justify-between mb-3">
//                   <div className="flex-1">
//                     <div className="flex items-center gap-2">
//                       <span className={`px-2 py-0.5 text-xs font-medium rounded ${
//                         item.type === 'item'
//                           ? 'bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300'
//                           : 'bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300'
//                       }`}>
//                         {item.type === 'item' ? 'Item' : 'Fee'}
//                       </span>
//                       <h4 className="text-sm font-medium text-gray-900 dark:text-white">{item.label}</h4>
//                     </div>
//                   </div>
//                 </div>

//                 <div className="flex items-center justify-between gap-6 text-sm bg-white dark:bg-gray-700 p-3 rounded">
//                   <div className="flex items-center gap-2">
//                     <span className="text-xs text-gray-500 dark:text-gray-400">Full:</span>
//                     <span className="font-semibold text-gray-900 dark:text-white text-base">
//                       ${item.originalAmount.toFixed(2)}
//                     </span>
//                   </div>
//                   <div className="flex items-center gap-2">
//                     <span className="text-xs text-gray-500 dark:text-gray-400">Refund Amount:</span>
//                     <span className="font-bold text-red-600 dark:text-red-400 text-base">
//                       ${item.refundAmount.toFixed(2)}
//                     </span>
//                   </div>
//                   <div className="flex items-center gap-2">
//                     <span className="text-xs text-gray-500 dark:text-gray-400">Remaining:</span>
//                     <span className="font-semibold text-blue-600 dark:text-blue-400 text-base">
//                       ${item.newAmount.toFixed(2)}
//                     </span>
//                   </div>
//                 </div>
//               </div>
//             ))}
//           </div>

//           {/* Summary */}
//           <div className="p-4 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 rounded-lg border-2 border-red-200 dark:border-red-800">
//             <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">Refund Summary</h3>
//             <div className="space-y-2">
//               <div className="flex justify-between text-base">
//                 <span className="font-bold text-gray-900 dark:text-white">Total Refund Amount:</span>
//                 <span className="font-bold text-red-600 dark:text-red-400 text-lg">
//                   ${totalRefund.toFixed(2)}
//                 </span>
//               </div>
//             </div>
//           </div>

//           {/* Warning */}
//           <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
//             <p className="text-xs text-gray-600 dark:text-gray-400">
//               <strong>Note:</strong> This action cannot be undone. The refund will be processed immediately and the customer will be notified.
//             </p>
//           </div>
//         </div>

//         {/* Footer */}
//         <div className="flex items-center justify-end gap-3 px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
//           <Button
//             variant="outline"
//             onClick={onClose}
//             disabled={isLoading}
//           >
//             Cancel
//           </Button>
//           <Button
//             onClick={onConfirm}
//             disabled={isLoading}
//             className="bg-red-600 hover:bg-red-700 text-white"
//           >
//             {isLoading ? (
//               <span className="flex items-center gap-2">
//                 <svg className="animate-spin h-4 w-4" fill="none" viewBox="0 0 24 24">
//                   <circle className="opacity-25" cx="12" cy="12" r="10" stroke="currentColor" strokeWidth="4"></circle>
//                   <path className="opacity-75" fill="currentColor" d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"></path>
//                 </svg>
//                 Processing...
//               </span>
//             ) : (
//               `Confirm Refund ($${totalRefund.toFixed(2)})`
//             )}
//           </Button>
//         </div>
//       </div>
//     </div>
//   );
// }


"use client";
import React, { useState } from "react";
import { Button } from "@/components/ui/button";

interface RefundItem {
  id: string;
  label: string;
  originalAmount: number;
  newAmount: number;
  refundAmount: number;
  type: "item" | "fee";
}

interface RefundConfirmationModalProps {
  isOpen: boolean;
  onClose: () => void;
  onConfirm: (refundReason: string) => void;
  refundItems: RefundItem[];
  isLoading?: boolean;
}

export function RefundConfirmationModal({
  isOpen,
  onClose,
  onConfirm,
  refundItems,
  isLoading = false,
}: RefundConfirmationModalProps) {
  const [refundReason, setRefundReason] = useState("");

  if (!isOpen) return null;

  const totalRefund = refundItems.reduce(
    (sum, item) => sum + item.refundAmount,
    0
  );

  return (
    <div className="fixed inset-0 z-[9999] bg-black/50 backdrop-blur-sm flex items-center justify-center p-4">
      <div className="w-full max-w-2xl bg-white dark:bg-gray-900 rounded-lg shadow-2xl border border-gray-200 dark:border-gray-700">
        {/* Header */}
        <div className="flex items-center justify-between px-6 py-4 border-b border-gray-200 dark:border-gray-700">
          <div>
            <h2 className="text-xl font-bold text-gray-900 dark:text-white">
              Confirm Refund
            </h2>
            <p className="text-sm text-gray-500 dark:text-gray-400 mt-1">
              Review the refund details before proceeding
            </p>
          </div>
          <button
            onClick={onClose}
            disabled={isLoading}
            className="text-gray-400 hover:text-gray-600 dark:hover:text-gray-300"
          >
            <svg
              className="w-6 h-6"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth={2}
                d="M6 18L18 6M6 6l12 12"
              />
            </svg>
          </button>
        </div>

        {/* Content */}
        <div className="max-h-[60vh] overflow-y-auto px-6 py-4">
          {/* Info Banner */}
          <div className="mb-4 p-4 bg-yellow-50 dark:bg-yellow-900/20 border border-yellow-200 dark:border-yellow-800 rounded-lg">
            <div className="flex items-start gap-3">
              <svg
                className="w-5 h-5 text-yellow-600 dark:text-yellow-400 flex-shrink-0 mt-0.5"
                fill="currentColor"
                viewBox="0 0 20 20"
              >
                <path
                  fillRule="evenodd"
                  d="M8.257 3.099c.765-1.36 2.722-1.36 3.486 0l5.58 9.92c.75 1.334-.213 2.98-1.742 2.98H4.42c-1.53 0-2.493-1.646-1.743-2.98l5.58-9.92zM11 13a1 1 0 11-2 0 1 1 0 012 0zm-1-8a1 1 0 00-1 1v3a1 1 0 002 0V6a1 1 0 00-1-1z"
                  clipRule="evenodd"
                />
              </svg>
              <div className="flex-1">
                <h3 className="text-sm font-semibold text-yellow-800 dark:text-yellow-400">
                  Refund Action Required
                </h3>
                <p className="text-sm text-yellow-700 dark:text-yellow-500 mt-1">
                  The price has been reduced. A refund will be processed for the
                  difference.
                </p>
              </div>
            </div>
          </div>

          {/* Refund Items */}
          <div className="space-y-3 mb-4">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white">
              Items to Refund:
            </h3>
            {refundItems.map((item) => (
              <div
                key={item.id}
                className="p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700"
              >
                <div className="flex items-start justify-between mb-3">
                  <div className="flex-1">
                    <div className="flex items-center gap-2">
                      <span
                        className={`px-2 py-0.5 text-xs font-medium rounded ${
                          item.type === "item"
                            ? "bg-blue-100 text-blue-700 dark:bg-blue-900 dark:text-blue-300"
                            : "bg-purple-100 text-purple-700 dark:bg-purple-900 dark:text-purple-300"
                        }`}
                      >
                        {item.type === "item" ? "Item" : "Fee"}
                      </span>
                      <h4 className="text-sm font-medium text-gray-900 dark:text-white">
                        {item.label}
                      </h4>
                    </div>
                  </div>
                </div>

                <div className="flex items-center justify-between gap-6 text-sm bg-white dark:bg-gray-700 p-3 rounded">
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      Full:
                    </span>
                    <span className="font-semibold text-gray-900 dark:text-white text-base">
                      ${item.originalAmount.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      Refund Amount:
                    </span>
                    <span className="font-bold text-red-600 dark:text-red-400 text-base">
                      ${item.refundAmount.toFixed(2)}
                    </span>
                  </div>
                  <div className="flex items-center gap-2">
                    <span className="text-xs text-gray-500 dark:text-gray-400">
                      Remaining:
                    </span>
                    <span className="font-semibold text-blue-600 dark:text-blue-400 text-base">
                      ${item.newAmount.toFixed(2)}
                    </span>
                  </div>
                </div>
              </div>
            ))}
          </div>

          {/* Refund Reason Input (visible & highlighted) */}
          <div className="mt-4 mb-4 p-4 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <label className="block text-sm font-semibold text-gray-900 dark:text-white mb-2">
              Refund Reason <span className="text-red-500">*</span>
            </label>
            <input
              type="text"
              value={refundReason}
              onChange={(e) => setRefundReason(e.target.value)}
              placeholder="Enter refund reason"
              className="w-full px-3 py-2 border border-gray-300 dark:border-gray-700 rounded-md bg-white dark:bg-gray-900 text-gray-900 dark:text-gray-100 focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>

          {/* Refund Summary */}
          <div className="p-4 bg-gradient-to-r from-red-50 to-orange-50 dark:from-red-900/20 dark:to-orange-900/20 rounded-lg border-2 border-red-200 dark:border-red-800">
            <h3 className="text-sm font-semibold text-gray-900 dark:text-white mb-3">
              Refund Summary
            </h3>
            <div className="flex justify-between text-base">
              <span className="font-bold text-gray-900 dark:text-white">
                Total Refund Amount:
              </span>
              <span className="font-bold text-red-600 dark:text-red-400 text-lg">
                ${totalRefund.toFixed(2)}
              </span>
            </div>
          </div>

          {/* Warning */}
          <div className="mt-4 p-3 bg-gray-50 dark:bg-gray-800 rounded-lg border border-gray-200 dark:border-gray-700">
            <p className="text-xs text-gray-600 dark:text-gray-400">
              <strong>Note:</strong> This action cannot be undone. The refund
              will be processed immediately and the customer will be notified.
            </p>
          </div>
        </div>

        {/* Footer */}
        <div className="flex items-center justify-end gap-3 px-6 py-4 border-t border-gray-200 dark:border-gray-700 bg-gray-50 dark:bg-gray-800/50">
          <Button variant="outline" onClick={onClose} disabled={isLoading}>
            Cancel
          </Button>
          <Button
            onClick={() => onConfirm(refundReason)}
            disabled={isLoading || !refundReason.trim()}
            className="bg-red-600 hover:bg-red-700 text-white"
          >
            {isLoading ? (
              <span className="flex items-center gap-2">
                <svg
                  className="animate-spin h-4 w-4"
                  fill="none"
                  viewBox="0 0 24 24"
                >
                  <circle
                    className="opacity-25"
                    cx="12"
                    cy="12"
                    r="10"
                    stroke="currentColor"
                    strokeWidth="4"
                  ></circle>
                  <path
                    className="opacity-75"
                    fill="currentColor"
                    d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
                  ></path>
                </svg>
                Processing...
              </span>
            ) : (
              `Confirm Refund ($${totalRefund.toFixed(2)})`
            )}
          </Button>
        </div>
      </div>
    </div>
  );
}
