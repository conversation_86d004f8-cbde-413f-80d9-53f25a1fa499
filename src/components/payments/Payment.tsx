
"use client";
import React, { useState } from "react";
import PageContainer from "@/components/ui/Page/PageContainer";
import RefundableOrdersTable from "@/components/payments/RefundableOrdersTable";
// import RefundModal from "@/components/payments/RefundModal";
import { useRouter } from "next/navigation";

export interface PaymentProps {
	title?: string;
	subtitle?: string;
}

export function Payment({
	title = "Orders",
	subtitle = "Manage and track refundable orders",
}: PaymentProps) {
	const router = useRouter();
	const [refundOrderId, setRefundOrderId] = useState<string | null>(null);

	return (
		<div className="container-fluid h-screen flex flex-col overflow-hidden">
			{/* Fixed Header */}
			

			{/* Full height scrollable table container */}
			<div className="flex-1 overflow-hidden">
				<RefundableOrdersTable
					onView={(id) => router.push(`/payments/Orderdetails/${id}`)}
					onRefund={(id) => setRefundOrderId(String(id))}
					onRowClick={(id) => router.push(`/payments/Orderdetails/${id}`)}
				/>
			</div>

			{/* Modals */}
			{/* {refundOrderId && (
				<RefundModal 
					orderId={refundOrderId} 
					onClose={() => setRefundOrderId(null)} 
				/>
			)} */}
		</div>
	);
}