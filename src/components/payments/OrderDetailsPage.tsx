"use client";
import React, { useState, useMemo } from "react";
import { useGetOrderDetailsByOrderId, useGetRefundOrders, useRefundOrder, useGetReceipt } from "@/hooks/api/usePayment";
import { useCancelOrder } from "@/hooks/api/useOrder";
import { useQueryClient } from "@tanstack/react-query";
import Loading from "@/app/(app)/loading";
import { FiChevronLeft, FiChevronRight, FiArrowLeft } from "react-icons/fi";
import { useRouter, usePathname } from "next/navigation";
import { useToast } from "@/hooks/useToast";
import { OrderIcon, CalendarIcon, formatEpochSeconds, formatDateTime, formatOrderStatus, getStatusStyle } from "./orderDetailsUtils";
import { OrderTabsContent } from "./OrderTabsContent";
import { RefundConfirmationModal } from "./RefundConfirmationModal";
import { OrderItemfeeRefundModal } from "./OrderItemfeeRefundModal";
import { CancelOrderModal } from "./CancelOrderModal";
import { useMyProfile } from "@/hooks/providers/useMyProfile";
import { requests } from "@/utils/agent";

// Info Card Component
const InfoCard = ({ icon: Icon, label, value, valueColor = "text-gray-800 dark:text-gray-200", bgColor = "bg-gray-100 dark:bg-gray-700" }: {
  icon: React.ComponentType<any>;
  label: string;
  value: string;
  valueColor?: string;
  bgColor?: string;
}) => (
  <div className="flex items-start space-x-2 sm:space-x-3">
    <div className={`p-1.5 sm:p-2 lg:p-2.5 ${bgColor} rounded flex-shrink-0`}>
      <Icon />
    </div>
    <div className="min-w-0 flex-1">
      <p className="text-[11px] sm:text-xs lg:text-sm text-gray-500 dark:text-gray-400 mb-0.5">{label}</p>
      <p className={`text-xs sm:text-sm lg:text-base font-medium ${valueColor} truncate`}>{value}</p>
    </div>
  </div>
);

// Order Summary Sidebar Component
const OrderSummary = ({ order, totalRefunded, data }: { order: any; totalRefunded: number; data: any }) => {
  const remainingAmount = (order?.total || 0) - totalRefunded;
  const refundPercentage = order?.total ? (totalRefunded / order.total) * 100 : 0;
  const itemCount = order?.items?.filter((item: any) => item.primaryDisplay)?.length || 0;
  const paymentMethod = (data as any)?.payments?.[0]?.paymentMethod || 'N/A';

  return (
    <div className="bg-white dark:bg-gray-800 rounded-xl shadow-lg border border-gray-200 dark:border-gray-700 overflow-hidden sticky top-6">
      {/* Header */}
      <div className="bg-gradient-to-r from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 px-4 sm:px-6 py-4 border-b border-gray-200 dark:border-gray-600">
        <div className="flex items-center space-x-3">
          <div className="p-2 bg-gray-100 dark:bg-gray-700 rounded-lg">
            <svg className="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6m0 10v-3m-3 3h.01M9 17h.01M9 14h.01M12 14h.01M15 11h.01M12 11h.01M9 11h.01M7 21h10a2 2 0 002-2V5a2 2 0 00-2-2H7a2 2 0 00-2 2v14a2 2 0 002 2z" />
            </svg>
          </div>
          <div>
            <h3 className="text-lg font-bold text-gray-900 dark:text-white">Order Summary</h3>
            <p className="text-sm text-gray-600 dark:text-gray-300">{itemCount} item{itemCount !== 1 ? 's' : ''}</p>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="p-4 sm:p-6 space-y-5">
        {/* Financial Summary */}
        <div className="space-y-4">
          {/* Amount Breakdown Summary */}
          <div className="bg-gray-50 dark:bg-gray-700/30 p-4 rounded-lg">
            <h4 className="text-sm font-semibold text-gray-700 dark:text-gray-300 mb-3">Amount Breakdown</h4>
            <div className="space-y-2 text-sm">
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Original Order:</span>
                <span className="font-semibold text-gray-900 dark:text-white">${order?.total?.toFixed(2) || '0.00'}</span>
              </div>
              <div className="flex justify-between">
                <span className="text-gray-600 dark:text-gray-400">Total Refunded:</span>
                <span className="font-semibold text-orange-600 dark:text-orange-400">-${totalRefunded.toFixed(2)}</span>
              </div>
              <div className="border-t border-gray-300 dark:border-gray-600 pt-2 mt-2">
                <div className="flex justify-between">
                  <span className="font-semibold text-gray-700 dark:text-gray-300">Current Balance:</span>
                  <span className="font-bold text-green-600 dark:text-green-400">${remainingAmount.toFixed(2)}</span>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default function OrderDetailsPage({ orderId }: { orderId: string }) {
  const router = useRouter();
  const pathname = usePathname();
  const queryClient = useQueryClient();
  const { data, isLoading, error } = useGetOrderDetailsByOrderId(orderId);
  const { data: ordersData } = useGetRefundOrders(1, 1000);
  const refundMutation = useRefundOrder();
  const { data: receiptData } = useGetReceipt(orderId);
  const cancelOrderMutation = useCancelOrder();
  const { toast } = useToast();
  const { hasPermissions } = useMyProfile();
  const [activeTab, setActiveTab] = useState<'items' | 'payments' | 'refunds' | 'history'>('items');
  const [expandedItems, setExpandedItems] = useState<Set<number>>(new Set());
  const [selectedItems, setSelectedItems] = useState<Set<number>>(new Set());
  const [selectedFees, setSelectedFees] = useState<Set<string>>(new Set());

  // Refund confirmation modal state
  const [showRefundModal, setShowRefundModal] = useState(false);
  const [pendingRefundItems, setPendingRefundItems] = useState<Array<{
    id: string;
    label: string;
    originalAmount: number;
    newAmount: number;
    refundAmount: number;
    type: 'item' | 'fee';
    parentId: string;
    parentTable: string;
  }>>([]);

  // Cancel order modal state
  const [showCancelModal, setShowCancelModal] = useState(false);

  // Fee-level refund modal state
  const [showFeeRefundModal, setShowFeeRefundModal] = useState(false);
  const [pendingFeeRefund, setPendingFeeRefund] = useState<{
    id: string;
    label: string;
    originalAmount: number;
    newAmount: number;
    refundAmount: number;
    type: 'fee';
    parentId: string;
    parentTable: string;
  } | null>(null);

  // Navigation logic
  const ordersList = useMemo(() => ordersData?.items ?? [], [ordersData]);

  const currentIndex = useMemo(() =>
    ordersList.findIndex((order: any) => order.orderId === orderId),
    [ordersList, orderId]
  );
  const hasPrevious = currentIndex > 0;
  const hasNext = currentIndex >= 0 && currentIndex < ordersList.length - 1;

  const getBaseRoute = () => {
    if (pathname?.includes('/payments/Orderdetails/')) {
      return '/payments/Orderdetails';
    }
    return '/payments/refunds';
  };

  const navigateToTable = () => {
    router.push('/payments/all');
  };

  const navigateToPrevious = () => {
    if (hasPrevious) {
      const previousOrder = ordersList[currentIndex - 1];
      router.push(`${getBaseRoute()}/${previousOrder.orderId}`);
    }
  };

  const navigateToNext = () => {
    if (hasNext) {
      const nextOrder = ordersList[currentIndex + 1];
      router.push(`${getBaseRoute()}/${nextOrder.orderId}`);
    }
  };

  // Memoized data calculations
  const { order, visibleItems, totalRefunded, isAllSelected, selectedItemsTotal, itemRefunds, feeRefunds } = useMemo(() => {
    const orderData: any = (data as any)?.orderInvoice ?? data;
    const paymentsData: any = (data as any)?.payments ?? data;
    const items: any[] = orderData?.items ?? [];
    const refundTransactions: any[] = (data as any)?.refundTransactions ?? [];

    const selectedTotal = items
      .filter(item => selectedItems.has(item.orderItemId))
      .reduce((sum, item) => sum + item.total, 0);

    // Calculate refunded amount per item and fee
    const itemRefundsMap = new Map<number, number>();
    const feeRefundsMap = new Map<string, number>();

    refundTransactions.forEach((refund: any) => {
      // Handle both refundItems and refundedDetail arrays
      const refundDetails = refund.refundItems || refund.refundedDetail || [];

      refundDetails.forEach((refundItem: any) => {
        // Handle different property names for parent table and ID
        const parentTable = refundItem.parentTable || refundItem.refundedParentTable;
        const parentId = refundItem.parentId || refundItem.refundedParentId;
        const amount = refundItem.amount || refundItem.refundedAmount || 0;

        // Match refund items to order items by checking parentId or other identifiers
        if (parentTable === 'orderItem') {
          items.forEach((item: any) => {
            if (parentId === item.orderItemUuid || parentId === String(item.orderItemId)) {
              const currentRefund = itemRefundsMap.get(item.orderItemId) || 0;
              itemRefundsMap.set(item.orderItemId, currentRefund + amount);
            }
          });
        } else if (parentTable === 'orderItemFee') {
          // Track fee refunds by UUID
          const currentFeeRefund = feeRefundsMap.get(parentId) || 0;
          feeRefundsMap.set(parentId, currentFeeRefund + amount);
        }
      });
    });

    return {
      order: orderData,
      visibleItems: items.filter(item => item.primaryDisplay),
      totalRefunded: refundTransactions.reduce((sum, refund) => sum + (refund.refundedTotal || 0), 0),
      isAllSelected: selectedItems.size === items.filter(item => item.primaryDisplay).length && items.length > 0,
      selectedItemsTotal: selectedTotal,
      itemRefunds: itemRefundsMap,
      feeRefunds: feeRefundsMap
    };
  }, [data, selectedItems]);

  // Handler functions
  const toggleItemExpansion = (itemId: number) => {
    setExpandedItems(prev => {
      const next = new Set(prev);
      next.has(itemId) ? next.delete(itemId) : next.add(itemId);
      return next;
    });
  };

  const toggleItemSelection = (itemId: number) => {
    const item = visibleItems.find((i: any) => i.orderItemId === itemId);
    const isCurrentlySelected = selectedItems.has(itemId);

    if (isCurrentlySelected) {
      setSelectedItems(prev => {
        const next = new Set(prev);
        next.delete(itemId);
        return next;
      });

      if (item?.fees) {
        const itemFeeUuids = item.fees.map((fee: any) => fee.orderItemFeeUuid);
        setSelectedFees(prev => {
          const next = new Set(prev);
          itemFeeUuids.forEach((uuid: string) => next.delete(uuid));
          return next;
        });
      }
    } else {
      setSelectedItems(prev => {
        const next = new Set(prev);
        next.add(itemId);
        return next;
      });

      if (item?.fees) {
        const itemFeeUuids = item.fees.map((fee: any) => fee.orderItemFeeUuid);
        setSelectedFees(prev => {
          const next = new Set(prev);
          itemFeeUuids.forEach((uuid: string) => next.add(uuid));
          return next;
        });
      }
    }
  };

  const toggleSelectAll = () => {
    const isCurrentlyAllSelected = selectedItems.size === visibleItems.length;

    if (isCurrentlyAllSelected) {
      setSelectedItems(new Set());
      setSelectedFees(new Set());
    } else {
      setSelectedItems(new Set(visibleItems.map(item => item.orderItemId)));
      const allFeeUuids = visibleItems.flatMap(item =>
        (item.fees ?? []).map((fee: any) => fee.orderItemFeeUuid)
      );
      setSelectedFees(new Set(allFeeUuids));
    }
  };

  const toggleFeeSelection = (feeUuid: string) => {
    setSelectedFees(prev => {
      const next = new Set(prev);
      next.has(feeUuid) ? next.delete(feeUuid) : next.add(feeUuid);
      return next;
    });
  };

  // Store refund amounts temporarily (before checkbox selection)
  const [tempItemRefunds, setTempItemRefunds] = useState<Map<number, { refundAmount: number, originalPrice: number, label: string, parentId: string }>>(new Map());
  const [tempFeeRefunds, setTempFeeRefunds] = useState<Map<string, { refundAmount: number, originalPrice: number, label: string }>>(new Map());

  const updateItemPrice = (itemId: number, refundAmount: number, originalPrice: number, itemLabel: string) => {
    // The newPrice entered by user IS the refund amount
    const balanceAmount = originalPrice - refundAmount;

    if (refundAmount > 0) {
      // Find the item to get its UUID
      const item = visibleItems.find((i: any) => i.orderItemId === itemId);
      const parentId = item?.orderItemUuid ?? item?.orderItemId ?? itemId;

      // Store the refund amount temporarily
      setTempItemRefunds(prev => {
        const next = new Map(prev);
        next.set(itemId, {
          refundAmount,
          originalPrice,
          label: itemLabel,
          parentId: String(parentId)
        });
        return next;
      });

      // Don't show modal immediately, let user select via checkbox
      toast.success({
        label: "Refund amount saved",
        message: `Refund amount of $${refundAmount.toFixed(2)} saved for ${itemLabel}. Select the checkbox to process the refund.`
      });
    }
  };

  const updateFeePrice = (feeUuid: string, refundAmount: number, originalPrice: number, feeLabel: string) => {
    // The newPrice entered by user IS the refund amount
    const balanceAmount = originalPrice - refundAmount;

    if (refundAmount > 0) {
      // Store the refund amount temporarily
      setTempFeeRefunds(prev => {
        const next = new Map(prev);
        next.set(feeUuid, {
          refundAmount,
          originalPrice,
          label: feeLabel
        });
        return next;
      });

      // Don't show modal immediately, let user select via checkbox
      toast.success({
        label: "Refund amount saved",
        message: `Refund amount of $${refundAmount.toFixed(2)} saved for ${feeLabel}. Select the checkbox to process the refund.`
      });
    }
  };

  const openFeeRefundModal = (feeUuid: string) => {
    const temp = tempFeeRefunds.get(feeUuid);
    if (!temp || temp.refundAmount <= 0) {
      toast.error({ label: "No refund amount", message: "Please enter and save a refund amount for this fee." });
      return;
    }
    const originalAmount = temp.originalPrice;
    const refundAmount = temp.refundAmount;
    const newAmount = originalAmount - refundAmount;
    setPendingFeeRefund({
      id: feeUuid,
      label: temp.label,
      originalAmount,
      newAmount,
      refundAmount,
      type: 'fee',
      parentId: feeUuid,
      parentTable: 'orderItemFee'
    });
    setShowFeeRefundModal(true);
  };

  const handleConfirmFeeRefund = async () => {
    if (!pendingFeeRefund) return;

    const payments: any[] = (data as any)?.payments ?? [];
    const paymentId = payments[0]?.paymentId;
    if (!paymentId) {
      toast.error({ label: "Refund failed", message: "No payment ID found" });
      return;
    }

    const payload = {
      orderId,
      paymentId,
      refundItems: [
        {
          parentId: pendingFeeRefund.parentId,
          parentTable: pendingFeeRefund.parentTable,
          amount: pendingFeeRefund.refundAmount
        }
      ]
    };

    try {
      await refundMutation.mutateAsync(payload);
      toast.success({
        label: "Refund successful",
        message: `Refund of $${pendingFeeRefund.refundAmount.toFixed(2)} processed successfully`
      });
      await queryClient.invalidateQueries({ queryKey: ["payments", "order", orderId] });
      setShowFeeRefundModal(false);
      setPendingFeeRefund(null);
      setTempFeeRefunds(prev => {
        const next = new Map(prev);
        next.delete(payload.refundItems[0].parentId);
        return next;
      });
      setSelectedFees(prev => {
        const next = new Set(prev);
        next.delete(payload.refundItems[0].parentId);
        return next;
      });
    } catch (e) {
      console.error("Refund failed:", e);
      toast.error({ label: "Refund failed", message: "Please try again or check logs." });
    }
  };

  // Handle partial/full refund button click
  const handlePartialRefund = () => {
    // Calculate total refund amount for selected items first
    let totalRefundAmount = 0;
    selectedItems.forEach(itemId => {
      const tempRefund = tempItemRefunds.get(itemId);
      if (tempRefund) {
        totalRefundAmount += tempRefund.refundAmount;
      }
    });
    selectedFees.forEach(feeUuid => {
      const tempRefund = tempFeeRefunds.get(feeUuid);
      if (tempRefund) {
        totalRefundAmount += tempRefund.refundAmount;
      }
    });

    // Check if this is a full order refund (all items selected AND refunding the entire remaining amount)
    const orderTotal = order?.total || 0;
    const remainingAmount = orderTotal - totalRefunded;
    const isFullOrderRefund = isAllSelected && totalRefundAmount >= remainingAmount;

    if (isFullOrderRefund) {
      // Full order-level refund
      if (remainingAmount <= 0) {
        toast.error({
          label: "No refund available",
          message: "The order has already been fully refunded."
        });
        return;
      }

      // Get the first item's UUID for the order reference
      const firstItem = visibleItems[0];
      const orderParentId = firstItem?.orderItemUuid ?? firstItem?.orderItemId ?? firstItem?.id;

      setPendingRefundItems([{
        id: orderId,
        label: `Full Order #${order?.orderNumber || orderId?.substring(0, 8) || 'N/A'}`,
        originalAmount: orderTotal,
        newAmount: 0,
        refundAmount: remainingAmount,
        type: 'item',
        parentId: orderParentId,
        parentTable: 'order'
      }]);
      setShowRefundModal(true);
      return;
    }

    // Partial refund - individual items/fees
    const refundItemsList: Array<{
      id: string;
      label: string;
      originalAmount: number;
      newAmount: number;
      refundAmount: number;
      type: 'item' | 'fee';
      parentId: string;
      parentTable: string;
    }> = [];

    // Add selected items with saved refund amounts
    selectedItems.forEach(itemId => {
      const tempRefund = tempItemRefunds.get(itemId);
      if (tempRefund) {
        const balanceAmount = tempRefund.originalPrice - tempRefund.refundAmount;
        // Find the actual item to get more details for better labeling
        const item = visibleItems.find((i: any) => i.orderItemId === itemId);
        const itemLabel = item?.secondaryDisplay
          ? `${tempRefund.label} - ${item.secondaryDisplay}`
          : tempRefund.label;

        refundItemsList.push({
          id: String(itemId),
          label: itemLabel,
          originalAmount: tempRefund.originalPrice,
          newAmount: balanceAmount,
          refundAmount: tempRefund.refundAmount,
          type: 'item',
          parentId: tempRefund.parentId,
          parentTable: 'orderItem'
        });
      }
    });

    // Add selected fees with saved refund amounts
    selectedFees.forEach(feeUuid => {
      const tempRefund = tempFeeRefunds.get(feeUuid);
      if (tempRefund) {
        const balanceAmount = tempRefund.originalPrice - tempRefund.refundAmount;
        refundItemsList.push({
          id: feeUuid,
          label: tempRefund.label,
          originalAmount: tempRefund.originalPrice,
          newAmount: balanceAmount,
          refundAmount: tempRefund.refundAmount,
          type: 'fee',
          parentId: feeUuid,
          parentTable: 'orderItemFee'
        });
      }
    });

    if (refundItemsList.length > 0) {
      setPendingRefundItems(refundItemsList);
      setShowRefundModal(true);
    } else {
      toast.error({
        label: "No refund amounts",
        message: "Please edit and save refund amounts for the selected items before processing."
      });
    }
  };

const handleConfirmRefund = async (refundReason: string) => {
  if (pendingRefundItems.length === 0) return;

  const payments: any[] = (data as any)?.payments ?? [];
  const paymentId = payments[0]?.paymentId;

  if (!paymentId) {
    toast.error({ label: "Refund failed", message: "No payment ID found" });
    return;
  }

  if (!refundReason.trim()) {
    toast.error({ label: "Refund failed", message: "Refund reason is required" });
    return;
  }

  const refundItems = pendingRefundItems.map(item => ({
    parentId: item.parentId,
    parentTable: item.parentTable,
    amount: item.refundAmount
  }));

  const payload = {
    orderId,
    paymentId,
    refundReason, // ✅ added refund reason
    refundItems
  };

  try {
    await refundMutation.mutateAsync(payload);

    toast.success({
      label: "Refund successful",
      message: `Refund of $${pendingRefundItems
        .reduce((sum, item) => sum + item.refundAmount, 0)
        .toFixed(2)} processed successfully`
    });

    // Invalidate and refetch order details
    await queryClient.invalidateQueries({ queryKey: ["payments", "order", orderId] });

    // Clear all temporary data and close modal
    setShowRefundModal(false);
    setPendingRefundItems([]);
    setTempItemRefunds(new Map());
    setTempFeeRefunds(new Map());
    setSelectedItems(new Set());
    setSelectedFees(new Set());
  } catch (e) {
    console.error("Refund failed:", e);
    toast.error({
      label: "Refund failed",
      message: "Please try again or check logs."
    });
  }
};

  const handlePrintReceipt = async () => {
    const permitted = hasPermissions(["super-admin"]);

    if (!receiptData?.receipts?.length) {
      toast.error({ label: "Print failed", message: "No receipt available for this order" });
      return;
    }

    // Get the first receipt URL
    let receiptUrl = receiptData.receipts[0]?.receiptUrl;

    if (!receiptUrl) {
      toast.error({ label: "Print failed", message: "Receipt URL not found" });
      return;
    }

    try {
      // Process the URL similar to order success page
      const removePartOfUrl = "https://dev.clerkxpress.com/";
      let realUrl = receiptUrl.replace(removePartOfUrl, "");

      // Modify realUrl if user is not admin, add "/me" after the second "/"
      if (!permitted) {
        const indexOfSecondSlash = realUrl.indexOf("/", realUrl.indexOf("/") + 1);
        realUrl = [
          realUrl.slice(0, indexOfSecondSlash),
          "/me",
          realUrl.slice(indexOfSecondSlash),
        ].join("");
      }

      // Download the receipt as blob
      const file = await requests.get(realUrl, {
        responseType: "blob",
      });

      // Create a blob URL and open in new tab for printing
      const link = document.createElement("a");
      const blob = file as unknown as Blob;
      link.href = URL.createObjectURL(blob);
      link.target = "_blank";
      document.body.appendChild(link);
      link.click();
      document.body.removeChild(link);

      toast.success({ label: "Receipt opened", message: "Receipt opened in new tab" });
    } catch (error) {
      console.error("Failed to load receipt:", error);
      toast.error({ label: "Print failed", message: "Failed to load receipt. Please try again." });
    }
  };

  const handleCancelOrderClick = () => {
    setShowCancelModal(true);
  };

  const handleConfirmCancelOrder = async () => {
    try {
      await cancelOrderMutation.mutateAsync(orderId);
      toast.success({
        label: "Order cancelled",
        message: `Order #${order?.orderNumber || orderId?.substring(0, 8) || 'N/A'} has been cancelled successfully`
      });
      setShowCancelModal(false);
      // Optionally refresh or redirect
      router.push('/payments/all');
    } catch (e) {
      console.error("Cancel order failed:", e);
      toast.error({ label: "Cancel failed", message: "Failed to cancel order. Please try again." });
    }
  };

  if (isLoading) return <div className="py-6"><Loading fixed={false} /></div>;
  if (error) return <div className="m-5 p-4 rounded bg-red-50 border border-red-200 text-sm text-red-700">Failed to load order details.</div>;

  // Check if receipt is available
  const payments: any[] = (data as any)?.payments ?? [];
  const hasReceipt = payments.some(payment => payment.receiptId !== null && payment.receiptId !== undefined);

  return (
    <div className="h-screen overflow-y-auto bg-gray-50 dark:bg-gray-900">
      <div className="container mx-auto px-2 sm:px-4 py-3 sm:py-6 max-w-7xl pb-24">
        {/* Navigation Header */}
        <div className="mb-4 sm:mb-6">
          {/* Mobile Layout */}
          <div className="flex items-center justify-between mb-3 sm:hidden">
            <div className="flex items-center gap-2">
              <button
                onClick={navigateToTable}
                className="flex items-center gap-1 p-1.5 text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700 rounded-md transition-all"
                title="Back to Table"
              >
                <FiArrowLeft className="w-4 h-4" />
                <span className="text-sm font-medium">Back</span>
              </button>
            </div>
            <div className="flex items-center gap-1">
              <button
                onClick={navigateToPrevious}
                disabled={!hasPrevious}
                className={`p-1.5 rounded-md transition-all ${
                  hasPrevious
                    ? 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                    : 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
                }`}
                title="Previous Order"
              >
                <FiChevronLeft className="w-5 h-5" />
              </button>
              <button
                onClick={navigateToNext}
                disabled={!hasNext}
                className={`p-1.5 rounded-md transition-all ${
                  hasNext
                    ? 'text-gray-700 dark:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                    : 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
                }`}
                title="Next Order"
              >
                <FiChevronRight className="w-5 h-5" />
              </button>
            </div>
          </div>

          {/* Desktop Layout */}
          <div className="hidden sm:flex sm:items-center justify-between">
            <div className="flex items-center gap-3">
              <button
                onClick={navigateToTable}
                className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-gray-700 dark:text-gray-200 bg-white dark:bg-gray-800 border border-gray-300 dark:border-gray-600 rounded-lg hover:bg-gray-50 dark:hover:bg-gray-700 transition-all"
              >
                <FiArrowLeft className="w-4 h-4" />
                <span>Back to Table</span>
              </button>
            </div>

            <div className="flex items-center gap-2">
              <button
                onClick={navigateToPrevious}
                disabled={!hasPrevious}
                className={`p-2 rounded-md transition-all ${
                  hasPrevious
                    ? 'text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700'
                    : 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
                }`}
                title="Previous Order"
              >
                <FiChevronLeft className="w-5 h-5" />
              </button>
              <button
                onClick={navigateToNext}
                disabled={!hasNext}
                className={`p-2 rounded-md transition-all ${
                  hasNext
                    ? 'text-gray-700 dark:text-gray-200 hover:bg-gray-200 dark:hover:bg-gray-700'
                    : 'text-gray-300 dark:text-gray-600 cursor-not-allowed'
                }`}
                title="Next Order"
              >
                <FiChevronRight className="w-5 h-5" />
              </button>
            </div>
          </div>
        </div>

        {/* Order Header */}
    <div className="bg-white dark:bg-gray-800 rounded-lg shadow-sm border border-gray-200 dark:border-gray-700 p-4 md:p-6 mb-4">
  {/* Header Section */}
  <div className="flex items-center justify-between gap-3 pb-4 mb-4 border-b border-gray-200 dark:border-gray-700">
    <div className="flex items-center gap-3">
      <div className="p-2.5 bg-gradient-to-br from-gray-50 to-gray-100 dark:from-gray-700 dark:to-gray-600 rounded-lg border border-gray-200 dark:border-gray-700 flex-shrink-0">
        <svg className="w-6 h-6 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 2l8 4v12l-8 4-8-4V6l8-4z" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 2v20" />
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M20 6l-8 4-8-4" />
        </svg>
      </div>
      <div>
        <h2 className="text-lg md:text-xl font-semibold text-gray-800 dark:text-gray-100 tracking-wide">
          Order #{order?.orderNumber || orderId?.substring(0, 8) || 'N/A'}
        </h2>
        <p className="text-xs text-gray-500 dark:text-gray-400 mt-0.5">Order Details & Summary</p>
      </div>
    </div>

    {/* Action Buttons */}
  
   <div className="flex items-center gap-2">
  <button
    onClick={handlePrintReceipt}
    disabled={!hasReceipt} // Using the hasReceipt boolean we defined earlier
    className={`flex items-center gap-2 px-3 py-2 text-sm font-medium rounded-lg transition-colors
      ${!hasReceipt
        ? 'bg-gray-400 cursor-not-allowed text-gray-200'
        : 'text-white bg-blue-600 hover:bg-blue-700 dark:bg-blue-500 dark:hover:bg-blue-600'
      }`}
    title={hasReceipt ? 'Print Receipt' : 'No receipt to print'}
  >
    <svg
      className="w-4 h-4"
      fill="none"
      stroke="currentColor"
      viewBox="0 0 24 24"
    >
      <path
        strokeLinecap="round"
        strokeLinejoin="round"
        strokeWidth={2}
        d="M17 17h2a2 2 0 002-2v-4a2 2 0 00-2-2H5a2 2 0 00-2 2v4a2 2 0 002 2h2m2 4h6a2 2 0 002-2v-4a2 2 0 00-2-2H9a2 2 0 00-2 2v4a2 2 0 002 2zm8-12V5a2 2 0 00-2-2H9a2 2 0 00-2 2v4h10z"
      />
    </svg>
    <span className="hidden sm:inline">Print Receipt</span>
  </button>


      <button
        onClick={handleCancelOrderClick}
        className="flex items-center gap-2 px-3 py-2 text-sm font-medium text-white bg-red-500 rounded-lg transition-colors"
        title="Cancel Order"
      >
        <svg className="w-4 h-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M6 18L18 6M6 6l12 12" />
        </svg>
        <span className="hidden sm:inline">Cancel Order</span>
      </button>
    </div>
  </div>

  {/* Order Details Grid */}
  <div className="space-y-3">
    {/* Status - Full Width on Mobile */}
    {order?.status && (
      <div className="flex items-center gap-3 p-3 bg-gray-50 dark:bg-gray-700/50 rounded-lg">
        <div className={`p-2 ${getStatusStyle(order.status).bg} rounded-lg flex-shrink-0`}>
          <svg className="w-5 h-5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z" />
          </svg>
        </div>
        <div className="flex-1">
          <p className="text-xs text-gray-500 dark:text-gray-400 mb-0.5">Order Status</p>
          <p className={`text-sm font-semibold ${getStatusStyle(order.status).text}`}>
            {formatOrderStatus(order.status)}
          </p>
        </div>
      </div>
    )}

    {/* Info Cards Grid - 1 col mobile, 2 cols tablet, 3 cols desktop */}
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-3">
      <InfoCard
        icon={CalendarIcon}
        label="Order Date"
        value={formatEpochSeconds(order?.createdDate / 1000)}
      />
      <InfoCard
        icon={() => (
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M3 10h18M7 15h1m4 0h1m-7 4h12a3 3 0 003-3V8a3 3 0 00-3-3H6a3 3 0 00-3 3v8a3 3 0 003 3z" />
          </svg>
        )}
        label="Payment Date"
        value={formatDateTime(order?.orderPaidDate)}
        bgColor="bg-gray-100 dark:bg-gray-700"
      />
      <InfoCard
        icon={() => (
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z" />
          </svg>
        )}
        label="Payee"
        value={(data as any)?.payments?.[0]?.payee?.firstName || 'N/A'}
        bgColor="bg-gray-100 dark:bg-gray-700"
      />
      <InfoCard
        icon={() => (
          <svg className="w-5 h-5 text-gray-600 dark:text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
          </svg>
        )}
        label="Total Refunded"
        value={`$${totalRefunded.toFixed(2)}`}
        valueColor="font-semibold text-gray-800 dark:text-gray-200"
        bgColor="bg-gray-100 dark:bg-gray-700"
      />
    </div>
  </div>
</div>

        {/* Main Content Grid */}
        <div className="grid grid-cols-1 xl:grid-cols-4 gap-4 sm:gap-6">
          <div className="xl:col-span-3">
            <OrderTabsContent
              activeTab={activeTab}
              setActiveTab={setActiveTab}
              visibleItems={visibleItems}
              data={data}
              selectedItems={selectedItems}
              expandedItems={expandedItems}
              toggleItemSelection={toggleItemSelection}
              toggleItemExpansion={toggleItemExpansion}
              selectedFees={selectedFees}
              toggleFeeSelection={toggleFeeSelection}
              updateItemPrice={updateItemPrice}
              updateFeePrice={updateFeePrice}
              toggleSelectAll={toggleSelectAll}
              isAllSelected={isAllSelected}
              order={order}
              itemRefunds={itemRefunds}
              feeRefunds={feeRefunds}
              tempItemRefunds={tempItemRefunds}
              tempFeeRefunds={tempFeeRefunds}
            onOpenFeeRefundModal={openFeeRefundModal}
            />
          </div>

          <div className="hidden xl:block">
            <OrderSummary order={order} totalRefunded={totalRefunded} data={data} />
          </div>
        </div>

        <div className="xl:hidden mt-4 sm:mt-6">
          <OrderSummary order={order} totalRefunded={totalRefunded} data={data} />
        </div>
      </div>

      {/* Sticky Bottom Action Bar (non-overlapping) */}
      {(selectedItems.size > 0 || selectedFees.size > 0) && activeTab === 'items' && (() => {
        // Calculate total refund amount for selected items
        let totalRefundAmount = 0;
        selectedItems.forEach(itemId => {
          const tempRefund = tempItemRefunds.get(itemId);
          if (tempRefund) {
            totalRefundAmount += tempRefund.refundAmount;
          }
        });
        selectedFees.forEach(feeUuid => {
          const tempRefund = tempFeeRefunds.get(feeUuid);
          if (tempRefund) {
            totalRefundAmount += tempRefund.refundAmount;
          }
        });

        // Count total selected items (items + standalone fees)
        const totalSelectedCount = selectedItems.size + selectedFees.size;
        const selectionLabel = selectedItems.size > 0
          ? `${selectedItems.size} item${selectedItems.size > 1 ? 's' : ''}${selectedFees.size > 0 ? ` + ${selectedFees.size} fee${selectedFees.size > 1 ? 's' : ''}` : ''}`
          : `${selectedFees.size} fee${selectedFees.size > 1 ? 's' : ''}`;

        return (
          <div className="sticky bottom-0 z-30">
            <div className="mx-auto w-full md:w-[90%] lg:w-[80%] xl:w-[70%] bg-white/95 dark:bg-gray-800/95 backdrop-blur border-t border-gray-200 dark:border-gray-700 px-3 sm:px-6 py-3 shadow-[0_-4px_12px_rgba(0,0,0,0.06)]">
              <div className="flex items-center justify-between gap-3">
                <div className="text-xs sm:text-sm text-gray-600 dark:text-gray-400">
                  <div className="whitespace-nowrap">{selectionLabel} selected</div>
                  <div className="font-semibold text-gray-900 dark:text-white whitespace-nowrap">
                    Total: ${totalRefundAmount > 0 ? totalRefundAmount.toFixed(2) : '0.00'}
                  </div>
                </div>
                <div className="flex items-center gap-2">
                  <button
                    className={`px-3 sm:px-6 py-1.5 sm:py-2 text-xs sm:text-sm text-white rounded-lg hover:opacity-90 transition-opacity font-medium whitespace-nowrap ${
                      isAllSelected ? 'bg-red-600' : 'bg-orange-600'
                    }`}
                    onClick={handlePartialRefund}
                  >
                    {isAllSelected ? 'Refund' : 'Partial Refund'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        );
      })()}

      {/* Refund Confirmation Modal */}
      <RefundConfirmationModal
        isOpen={showRefundModal}
        onClose={() => {
          setShowRefundModal(false);
          setPendingRefundItems([]);
        }}
        onConfirm={handleConfirmRefund}
        refundItems={pendingRefundItems}
        isLoading={refundMutation.isLoading}
      />

      {/* Fee-level Refund Modal */}
      <OrderItemfeeRefundModal
        isOpen={showFeeRefundModal}
        onClose={() => { setShowFeeRefundModal(false); setPendingFeeRefund(null); }}
        onConfirm={handleConfirmFeeRefund}
        refundItem={pendingFeeRefund}
        isLoading={refundMutation.isLoading}
      />

      {/* Cancel Order Modal */}
      <CancelOrderModal
        isOpen={showCancelModal}
        onClose={() => setShowCancelModal(false)}
        onConfirm={handleConfirmCancelOrder}
        orderNumber={order?.orderNumber || orderId?.substring(0, 8) || 'N/A'}
        isLoading={cancelOrderMutation.isLoading}
      />
    </div>
  );
}